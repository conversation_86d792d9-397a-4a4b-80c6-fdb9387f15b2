#!/usr/bin/env python3
"""
CSV Calendar Data Ingest for Advanced Calendar Management Agent
Integracja CSV data ingest z zaawansowanym agentem zarządzania kalendarzami.

Features:
- Automatyczne importowanie danych kalendarza z plików CSV
- Mapowanie kolumn CSV na struktury ServiceOrder i Technician
- Walidacja i czyszczenie danych podczas importu
- Batch processing dla dużych plików CSV
- Error handling i logging dla nieprawidłowych rekordów
- Integration z Advanced Calendar Management Agent
"""

import csv
import json
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime, timedelta, time
from dataclasses import dataclass, asdict
from pathlib import Path
import re

# Use basic logging if loguru not available
try:
    from loguru import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logging.basicConfig(level=logging.INFO)

from .advanced_calendar_management_agent import (
    AdvancedCalendarManagementAgent, ServiceType, Priority, TechnicianSkill,
    Location, ServiceOrder, Technician
)


@dataclass
class CSVImportResult:
    """Wynik importu CSV."""
    success_count: int = 0
    error_count: int = 0
    warnings: List[str] = None
    errors: List[str] = None
    imported_orders: List[str] = None
    imported_technicians: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []
        if self.imported_orders is None:
            self.imported_orders = []
        if self.imported_technicians is None:
            self.imported_technicians = []


class CSVCalendarIngest:
    """
    CSV Data Ingest dla Advanced Calendar Management Agent.
    
    Obsługuje importowanie:
    - Zleceń serwisowych (service_orders.csv)
    - Techników (technicians.csv)
    - Lokalizacji (locations.csv)
    - Harmonogramów (schedules.csv)
    """
    
    def __init__(self, calendar_agent: AdvancedCalendarManagementAgent):
        self.calendar_agent = calendar_agent
        self.geocoder_cache = {}  # Cache dla geocoding
        
        # Mapowanie kolumn CSV
        self.service_order_columns = {
            'id': 'order_id',
            'order_id': 'order_id',
            'zlecenie_id': 'order_id',
            'typ': 'service_type',
            'service_type': 'service_type',
            'typ_zlecenia': 'service_type',
            'priorytet': 'priority',
            'priority': 'priority',
            'klient': 'customer_name',
            'customer_name': 'customer_name',
            'nazwa_klienta': 'customer_name',
            'telefon': 'customer_phone',
            'customer_phone': 'customer_phone',
            'phone': 'customer_phone',
            'adres': 'address',
            'address': 'address',
            'lokalizacja': 'address',
            'czas_trwania': 'estimated_duration',
            'estimated_duration': 'estimated_duration',
            'duration': 'estimated_duration',
            'sprzet': 'equipment_info',
            'equipment_info': 'equipment_info',
            'equipment': 'equipment_info',
            'uwagi': 'special_requirements',
            'special_requirements': 'special_requirements',
            'notes': 'special_requirements',
            'data_utworzenia': 'created_at',
            'created_at': 'created_at',
            'deadline': 'deadline',
            'termin': 'deadline'
        }
        
        self.technician_columns = {
            'id': 'technician_id',
            'technician_id': 'technician_id',
            'tech_id': 'technician_id',
            'nazwa': 'name',
            'name': 'name',
            'imie_nazwisko': 'name',
            'umiejetnosci': 'skills',
            'skills': 'skills',
            'kompetencje': 'skills',
            'adres_bazy': 'base_address',
            'base_address': 'base_address',
            'baza': 'base_address',
            'godziny_pracy': 'working_hours',
            'working_hours': 'working_hours',
            'work_hours': 'working_hours',
            'max_zlecen': 'max_daily_orders',
            'max_daily_orders': 'max_daily_orders',
            'capacity': 'max_daily_orders',
            'stawka': 'hourly_rate',
            'hourly_rate': 'hourly_rate',
            'rate': 'hourly_rate',
            'predkosc': 'travel_speed_kmh',
            'travel_speed_kmh': 'travel_speed_kmh',
            'speed': 'travel_speed_kmh'
        }
        
        logger.info("CSV Calendar Ingest initialized")
    
    def import_from_csv(self, file_path: Union[str, Path], data_type: str = "auto") -> CSVImportResult:
        """
        Importuj dane z pliku CSV.
        
        Args:
            file_path: Ścieżka do pliku CSV
            data_type: Typ danych ("orders", "technicians", "locations", "auto")
            
        Returns:
            CSVImportResult z wynikami importu
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return CSVImportResult(
                error_count=1,
                errors=[f"File not found: {file_path}"]
            )
        
        # Auto-detect data type based on filename
        if data_type == "auto":
            filename = file_path.stem.lower()
            if any(keyword in filename for keyword in ['order', 'zlecen', 'service']):
                data_type = "orders"
            elif any(keyword in filename for keyword in ['tech', 'pracown', 'serwis']):
                data_type = "technicians"
            elif any(keyword in filename for keyword in ['location', 'lokalizac', 'adres']):
                data_type = "locations"
            else:
                data_type = "orders"  # Default
        
        logger.info(f"Importing {data_type} from {file_path}")
        
        try:
            # Read CSV with standard library
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                # Detect delimiter
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                # Read CSV data
                reader = csv.DictReader(csvfile, delimiter=delimiter)
                rows = list(reader)

            if data_type == "orders":
                return self._import_service_orders(rows)
            elif data_type == "technicians":
                return self._import_technicians(rows)
            elif data_type == "locations":
                return self._import_locations(rows)
            else:
                return CSVImportResult(
                    error_count=1,
                    errors=[f"Unknown data type: {data_type}"]
                )
                
        except Exception as e:
            logger.error(f"Failed to import CSV: {e}")
            return CSVImportResult(
                error_count=1,
                errors=[f"Import failed: {str(e)}"]
            )
    
    def _import_service_orders(self, rows: List[Dict[str, str]]) -> CSVImportResult:
        """Importuj zlecenia serwisowe z list of dictionaries."""
        result = CSVImportResult()

        # Normalize column names
        normalized_rows = self._normalize_rows(rows, self.service_order_columns)

        for index, row in enumerate(normalized_rows):
            try:
                # Extract and validate data
                order_data = self._extract_order_data(row)

                if not order_data:
                    result.error_count += 1
                    result.errors.append(f"Row {index + 1}: Invalid order data")
                    continue

                # Create ServiceOrder
                order = self._create_service_order(order_data)

                # Add to calendar agent
                self.calendar_agent.add_service_order(order)

                result.success_count += 1
                result.imported_orders.append(order.id)

                logger.debug(f"Imported order: {order.id}")

            except Exception as e:
                result.error_count += 1
                result.errors.append(f"Row {index + 1}: {str(e)}")
                logger.error(f"Failed to import order from row {index + 1}: {e}")

        logger.info(f"Service orders import completed: {result.success_count} success, {result.error_count} errors")
        return result
    
    def _import_technicians(self, rows: List[Dict[str, str]]) -> CSVImportResult:
        """Importuj techników z list of dictionaries."""
        result = CSVImportResult()

        # Normalize column names
        normalized_rows = self._normalize_rows(rows, self.technician_columns)

        for index, row in enumerate(normalized_rows):
            try:
                # Extract and validate data
                tech_data = self._extract_technician_data(row)

                if not tech_data:
                    result.error_count += 1
                    result.errors.append(f"Row {index + 1}: Invalid technician data")
                    continue

                # Create Technician
                technician = self._create_technician(tech_data)

                # Add to calendar agent
                self.calendar_agent.add_technician(technician)

                result.success_count += 1
                result.imported_technicians.append(technician.id)

                logger.debug(f"Imported technician: {technician.id}")

            except Exception as e:
                result.error_count += 1
                result.errors.append(f"Row {index + 1}: {str(e)}")
                logger.error(f"Failed to import technician from row {index + 1}: {e}")

        logger.info(f"Technicians import completed: {result.success_count} success, {result.error_count} errors")
        return result
    
    def _normalize_rows(self, rows: List[Dict[str, str]], column_mapping: Dict[str, str]) -> List[Dict[str, str]]:
        """Normalize column names using mapping."""
        if not rows:
            return []

        # Get original column names
        original_columns = list(rows[0].keys())

        # Create mapping from original to normalized names
        column_map = {}
        for col in original_columns:
            col_lower = col.lower().strip()
            if col_lower in column_mapping:
                column_map[col] = column_mapping[col_lower]

        # Normalize all rows
        normalized_rows = []
        for row in rows:
            normalized_row = {}
            for original_col, value in row.items():
                normalized_col = column_map.get(original_col, original_col)
                normalized_row[normalized_col] = value
            normalized_rows.append(normalized_row)

        return normalized_rows
    
    def _extract_order_data(self, row: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Extract and validate order data from CSV row."""
        try:
            # Required fields
            order_id = str(row.get('order_id', f"ORDER_{datetime.now().strftime('%Y%m%d_%H%M%S')}"))
            customer_name = str(row.get('customer_name', '')).strip()
            address = str(row.get('address', '')).strip()
            
            if not customer_name or not address:
                return None
            
            # Service type mapping
            service_type_str = str(row.get('service_type', 'serwis')).lower()
            service_type_map = {
                'oględziny': ServiceType.INSPECTION,
                'inspection': ServiceType.INSPECTION,
                'przegląd': ServiceType.INSPECTION,
                'montaż': ServiceType.INSTALLATION,
                'installation': ServiceType.INSTALLATION,
                'instalacja': ServiceType.INSTALLATION,
                'serwis': ServiceType.SERVICE_REPAIR,
                'service': ServiceType.SERVICE_REPAIR,
                'naprawa': ServiceType.SERVICE_REPAIR,
                'repair': ServiceType.SERVICE_REPAIR
            }
            service_type = service_type_map.get(service_type_str, ServiceType.SERVICE_REPAIR)
            
            # Priority mapping
            priority_str = str(row.get('priority', 'medium')).lower()
            priority_map = {
                'low': Priority.LOW,
                'niski': Priority.LOW,
                'medium': Priority.MEDIUM,
                'średni': Priority.MEDIUM,
                'high': Priority.HIGH,
                'wysoki': Priority.HIGH,
                'urgent': Priority.URGENT,
                'pilny': Priority.URGENT
            }
            priority = priority_map.get(priority_str, Priority.MEDIUM)
            
            # Duration
            duration_str = str(row.get('estimated_duration', '2')).strip()
            try:
                duration_hours = float(re.findall(r'\d+\.?\d*', duration_str)[0])
            except (IndexError, ValueError):
                duration_hours = 2.0
            
            return {
                'order_id': order_id,
                'service_type': service_type,
                'priority': priority,
                'customer_name': customer_name,
                'customer_phone': str(row.get('customer_phone', '')).strip(),
                'address': address,
                'estimated_duration': duration_hours,
                'equipment_info': str(row.get('equipment_info', '')).strip(),
                'special_requirements': str(row.get('special_requirements', '')).strip()
            }
            
        except Exception as e:
            logger.error(f"Failed to extract order data: {e}")
            return None

    def _extract_technician_data(self, row: Dict[str, str]) -> Optional[Dict[str, Any]]:
        """Extract and validate technician data from CSV row."""
        try:
            # Required fields
            tech_id = str(row.get('technician_id', f"TECH_{datetime.now().strftime('%Y%m%d_%H%M%S')}"))
            name = str(row.get('name', '')).strip()
            base_address = str(row.get('base_address', '')).strip()

            if not name or not base_address:
                return None

            # Skills mapping
            skills_str = str(row.get('skills', 'basic_maintenance')).lower()
            skill_map = {
                'basic': TechnicianSkill.BASIC_MAINTENANCE,
                'podstawowy': TechnicianSkill.BASIC_MAINTENANCE,
                'basic_maintenance': TechnicianSkill.BASIC_MAINTENANCE,
                'installation': TechnicianSkill.INSTALLATION,
                'montaż': TechnicianSkill.INSTALLATION,
                'instalacja': TechnicianSkill.INSTALLATION,
                'advanced': TechnicianSkill.ADVANCED_REPAIR,
                'zaawansowany': TechnicianSkill.ADVANCED_REPAIR,
                'advanced_repair': TechnicianSkill.ADVANCED_REPAIR,
                'electrical': TechnicianSkill.ELECTRICAL,
                'elektryczny': TechnicianSkill.ELECTRICAL,
                'refrigeration': TechnicianSkill.REFRIGERATION,
                'chłodnictwo': TechnicianSkill.REFRIGERATION
            }

            # Parse multiple skills
            skills = []
            for skill_part in skills_str.split(','):
                skill_part = skill_part.strip()
                if skill_part in skill_map:
                    skills.append(skill_map[skill_part])

            if not skills:
                skills = [TechnicianSkill.BASIC_MAINTENANCE]

            # Working hours
            working_hours_str = str(row.get('working_hours', '8:00-16:00')).strip()
            try:
                start_str, end_str = working_hours_str.split('-')
                start_time = datetime.strptime(start_str.strip(), '%H:%M').time()
                end_time = datetime.strptime(end_str.strip(), '%H:%M').time()
                working_hours = (start_time, end_time)
            except:
                working_hours = (time(8, 0), time(16, 0))

            # Other fields
            max_orders = int(row.get('max_daily_orders', 8))
            hourly_rate = float(row.get('hourly_rate', 80.0))
            travel_speed = float(row.get('travel_speed_kmh', 35.0))

            return {
                'technician_id': tech_id,
                'name': name,
                'skills': skills,
                'base_address': base_address,
                'working_hours': working_hours,
                'max_daily_orders': max_orders,
                'hourly_rate': hourly_rate,
                'travel_speed_kmh': travel_speed
            }

        except Exception as e:
            logger.error(f"Failed to extract technician data: {e}")
            return None

    def _create_service_order(self, order_data: Dict[str, Any]) -> ServiceOrder:
        """Create ServiceOrder from extracted data."""
        # Geocode address
        location = self._geocode_address(order_data['address'])

        return ServiceOrder(
            id=order_data['order_id'],
            service_type=order_data['service_type'],
            priority=order_data['priority'],
            location=location,
            customer_name=order_data['customer_name'],
            customer_phone=order_data['customer_phone'],
            estimated_duration=timedelta(hours=order_data['estimated_duration']),
            required_skills=[TechnicianSkill.BASIC_MAINTENANCE],  # Default
            preferred_time_slots=[],
            equipment_info={'description': order_data['equipment_info']},
            special_requirements=[order_data['special_requirements']] if order_data['special_requirements'] else []
        )

    def _create_technician(self, tech_data: Dict[str, Any]) -> Technician:
        """Create Technician from extracted data."""
        # Geocode base address
        base_location = self._geocode_address(tech_data['base_address'])

        return Technician(
            id=tech_data['technician_id'],
            name=tech_data['name'],
            skills=tech_data['skills'],
            base_location=base_location,
            working_hours=tech_data['working_hours'],
            max_daily_orders=tech_data['max_daily_orders'],
            hourly_rate=tech_data['hourly_rate'],
            travel_speed_kmh=tech_data['travel_speed_kmh']
        )

    def _geocode_address(self, address: str) -> Location:
        """Geocode address to Location object with caching."""
        if address in self.geocoder_cache:
            cached = self.geocoder_cache[address]
            return Location(
                address=address,
                latitude=cached['lat'],
                longitude=cached['lon'],
                district=cached.get('district')
            )

        try:
            # For demo purposes, use Warsaw coordinates with random offset
            import random
            base_lat, base_lon = 52.2297, 21.0122  # Warsaw center

            # Try to determine district from address
            district = self._extract_district_from_address(address)

            # Add some variation based on district
            district_offsets = {
                'Mokotów': (52.1951, 21.0450),
                'Śródmieście': (52.2297, 21.0122),
                'Wilanów': (52.1647, 21.0889),
                'Wola': (52.2394, 20.9803),
                'Żoliborz': (52.2656, 20.9814),
                'Praga-Południe': (52.2297, 21.0689),
                'Ursynów': (52.1397, 21.0489)
            }

            if district in district_offsets:
                lat, lon = district_offsets[district]
                lat += random.uniform(-0.02, 0.02)
                lon += random.uniform(-0.02, 0.02)
            else:
                lat = base_lat + random.uniform(-0.1, 0.1)
                lon = base_lon + random.uniform(-0.1, 0.1)

            # Cache result
            self.geocoder_cache[address] = {
                'lat': lat,
                'lon': lon,
                'district': district
            }

            return Location(
                address=address,
                latitude=lat,
                longitude=lon,
                district=district
            )

        except Exception as e:
            logger.warning(f"Geocoding failed for {address}: {e}")
            # Fallback to Warsaw center
            return Location(
                address=address,
                latitude=52.2297,
                longitude=21.0122,
                district="Warszawa"
            )

    def _extract_district_from_address(self, address: str) -> Optional[str]:
        """Extract district name from address string."""
        address_lower = address.lower()

        districts = [
            'mokotów', 'śródmieście', 'wilanów', 'wola', 'żoliborz',
            'praga-południe', 'praga-północ', 'ursynów', 'ochota',
            'targówek', 'bemowo', 'bielany', 'białołęka', 'włochy',
            'ursus', 'rembertów', 'wawer', 'wesołą'
        ]

        for district in districts:
            if district in address_lower:
                return district.title()

        return None

    def _import_locations(self, df: pd.DataFrame) -> CSVImportResult:
        """Import locations from DataFrame (for future use)."""
        result = CSVImportResult()
        result.warnings.append("Location import not yet implemented")
        return result

    def export_to_csv(self, output_dir: Union[str, Path], export_type: str = "all") -> Dict[str, str]:
        """
        Export current calendar data to CSV files.

        Args:
            output_dir: Directory to save CSV files
            export_type: Type of data to export ("orders", "technicians", "all")

        Returns:
            Dict with paths to created files
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        created_files = {}

        try:
            if export_type in ["orders", "all"]:
                orders_file = output_dir / f"service_orders_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                self._export_service_orders(orders_file)
                created_files['orders'] = str(orders_file)

            if export_type in ["technicians", "all"]:
                techs_file = output_dir / f"technicians_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                self._export_technicians(techs_file)
                created_files['technicians'] = str(techs_file)

            logger.info(f"Exported calendar data to {len(created_files)} files")
            return created_files

        except Exception as e:
            logger.error(f"Export failed: {e}")
            return {}

    def _export_service_orders(self, file_path: Path):
        """Export service orders to CSV."""
        orders_data = []

        for order in self.calendar_agent.pending_orders:
            orders_data.append({
                'order_id': order.id,
                'service_type': order.service_type.value,
                'priority': order.priority.name,
                'customer_name': order.customer_name,
                'customer_phone': order.customer_phone,
                'address': order.location.address,
                'district': order.location.district,
                'estimated_duration': order.estimated_duration.total_seconds() / 3600,
                'equipment_info': order.equipment_info.get('description', ''),
                'special_requirements': ', '.join(order.special_requirements),
                'created_at': order.created_at.isoformat(),
                'deadline': order.deadline.isoformat() if order.deadline else ''
            })

        df = pd.DataFrame(orders_data)
        df.to_csv(file_path, index=False, encoding='utf-8')
        logger.info(f"Exported {len(orders_data)} service orders to {file_path}")

    def _export_technicians(self, file_path: Path):
        """Export technicians to CSV."""
        techs_data = []

        for tech in self.calendar_agent.technicians:
            techs_data.append({
                'technician_id': tech.id,
                'name': tech.name,
                'skills': ', '.join([skill.value for skill in tech.skills]),
                'base_address': tech.base_location.address,
                'base_district': tech.base_location.district,
                'working_hours': f"{tech.working_hours[0].strftime('%H:%M')}-{tech.working_hours[1].strftime('%H:%M')}",
                'max_daily_orders': tech.max_daily_orders,
                'hourly_rate': tech.hourly_rate,
                'travel_speed_kmh': tech.travel_speed_kmh
            })

        df = pd.DataFrame(techs_data)
        df.to_csv(file_path, index=False, encoding='utf-8')
        logger.info(f"Exported {len(techs_data)} technicians to {file_path}")

    def create_sample_csv_files(self, output_dir: Union[str, Path]) -> Dict[str, str]:
        """Create sample CSV files for testing."""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Sample service orders
        orders_data = [
            {
                'order_id': 'ORDER_001',
                'service_type': 'serwis',
                'priority': 'high',
                'customer_name': 'Jan Kowalski',
                'customer_phone': '+48 123 456 789',
                'address': 'ul. Marszałkowska 100, Warszawa Śródmieście',
                'estimated_duration': '2.5',
                'equipment_info': 'LG S12ET klimatyzacja',
                'special_requirements': 'Dostęp przez balkon'
            },
            {
                'order_id': 'ORDER_002',
                'service_type': 'montaż',
                'priority': 'medium',
                'customer_name': 'Maria Nowak',
                'customer_phone': '+48 987 654 321',
                'address': 'ul. Puławska 50, Warszawa Mokotów',
                'estimated_duration': '4',
                'equipment_info': 'Daikin FTXS25K',
                'special_requirements': 'Nowa instalacja'
            }
        ]

        # Sample technicians
        techs_data = [
            {
                'technician_id': 'TECH_001',
                'name': 'Piotr Serwisant',
                'skills': 'installation,electrical',
                'base_address': 'ul. Woronicza 10, Warszawa Mokotów',
                'working_hours': '8:00-16:00',
                'max_daily_orders': '6',
                'hourly_rate': '85',
                'travel_speed_kmh': '35'
            },
            {
                'technician_id': 'TECH_002',
                'name': 'Anna Technik',
                'skills': 'advanced_repair,refrigeration',
                'base_address': 'ul. Nowy Świat 20, Warszawa Śródmieście',
                'working_hours': '7:30-15:30',
                'max_daily_orders': '8',
                'hourly_rate': '95',
                'travel_speed_kmh': '30'
            }
        ]

        # Save files
        orders_file = output_dir / 'sample_service_orders.csv'
        techs_file = output_dir / 'sample_technicians.csv'

        pd.DataFrame(orders_data).to_csv(orders_file, index=False, encoding='utf-8')
        pd.DataFrame(techs_data).to_csv(techs_file, index=False, encoding='utf-8')

        logger.info(f"Created sample CSV files in {output_dir}")

        return {
            'orders': str(orders_file),
            'technicians': str(techs_file)
        }
