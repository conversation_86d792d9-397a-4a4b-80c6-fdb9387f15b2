"""
HVAC Agents Package
Pakiet agentów dla systemu HVAC CRM.
"""

# Import available agents
try:
    from .calendar_processing_agent import CalendarProcessingAgent
except ImportError:
    CalendarProcessingAgent = None

try:
    from .advanced_calendar_management_agent import (
        AdvancedCalendarManagementAgent,
        ServiceType,
        Priority,
        TechnicianSkill,
        Location,
        ServiceOrder,
        Technician,
        ScheduledAppointment,
        WarsawDistrictOptimizer
    )
except ImportError:
    AdvancedCalendarManagementAgent = None
    ServiceType = None
    Priority = None
    TechnicianSkill = None
    Location = None
    ServiceOrder = None
    Technician = None
    ScheduledAppointment = None
    WarsawDistrictOptimizer = None

try:
    from .csv_calendar_ingest import (
        CSVCalendarIngest,
        CSVImportResult
    )
except ImportError:
    CSVCalendarIngest = None
    CSVImportResult = None

try:
    from .enhanced_a2a_protocol import (
        EnhancedA2AProtocol,
        CalendarManagerAgent,
        A2AAgent,
        A2AMessage,
        AgentType,
        MessageType,
        create_calendar_manager_agent,
        create_a2a_protocol_with_calendar
    )
except ImportError:
    EnhancedA2AProtocol = None
    CalendarManagerAgent = None
    A2AAgent = None
    A2AMessage = None
    AgentType = None
    MessageType = None
    create_calendar_manager_agent = None
    create_a2a_protocol_with_calendar = None

__all__ = [
    'CalendarProcessingAgent',
    'AdvancedCalendarManagementAgent',
    'ServiceType',
    'Priority',
    'TechnicianSkill',
    'Location',
    'ServiceOrder',
    'Technician',
    'ScheduledAppointment',
    'WarsawDistrictOptimizer',
    'CSVCalendarIngest',
    'CSVImportResult',
    'EnhancedA2AProtocol',
    'CalendarManagerAgent',
    'A2AAgent',
    'A2AMessage',
    'AgentType',
    'MessageType',
    'create_calendar_manager_agent',
    'create_a2a_protocol_with_calendar'
]
