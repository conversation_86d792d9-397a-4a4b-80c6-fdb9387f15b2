#!/usr/bin/env python3
"""
Enhanced Agent-to-Agent (A2A) Protocol for HVAC CRM System
Rozszerzony protokół A2A dla wszystkich agentów systemu HVAC CRM.

Features:
- Cross-agent communication dla optymalizacji harmonogramów
- Real-time data synchronization między agentami
- Event-driven architecture dla agent interactions
- Unified message format dla wszystkich typów agentów
- Integration z Calendar Management Agent
- Support dla email analysis, quote generation, equipment matching
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger
import weakref

try:
    from .advanced_calendar_management_agent import AdvancedCalendarManagementAgent
    from .csv_calendar_ingest import CSVCalendarIngest
    CALENDAR_AVAILABLE = True
except ImportError:
    logger.warning("Calendar agents not available")
    CALENDAR_AVAILABLE = False


class AgentType(Enum):
    """Typy agentów w systemie HVAC."""
    EMAIL_ANALYZER = "email_analyzer"
    CALENDAR_MANAGER = "calendar_manager"
    QUOTE_GENERATOR = "quote_generator"
    EQUIPMENT_MATCHER = "equipment_matcher"
    CUSTOMER_PROFILER = "customer_profiler"
    ROUTE_OPTIMIZER = "route_optimizer"
    MAINTENANCE_SCHEDULER = "maintenance_scheduler"
    PERFORMANCE_MONITOR = "performance_monitor"


class MessageType(Enum):
    """Typy wiadomości A2A."""
    REQUEST = "request"
    RESPONSE = "response"
    EVENT = "event"
    NOTIFICATION = "notification"
    COMMAND = "command"
    QUERY = "query"
    UPDATE = "update"


class Priority(Enum):
    """Priorytety wiadomości."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4


@dataclass
class A2AMessage:
    """Standardowa wiadomość A2A."""
    id: str
    sender_id: str
    sender_type: AgentType
    recipient_id: Optional[str]
    recipient_type: Optional[AgentType]
    message_type: MessageType
    priority: Priority
    payload: Dict[str, Any]
    timestamp: datetime
    correlation_id: Optional[str] = None
    expires_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.expires_at is None:
            # Default expiration: 1 hour for requests, 5 minutes for events
            if self.message_type in [MessageType.REQUEST, MessageType.QUERY]:
                self.expires_at = self.timestamp + timedelta(hours=1)
            else:
                self.expires_at = self.timestamp + timedelta(minutes=5)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        # Convert enums to strings
        data['sender_type'] = self.sender_type.value
        data['recipient_type'] = self.recipient_type.value if self.recipient_type else None
        data['message_type'] = self.message_type.value
        data['priority'] = self.priority.value
        data['timestamp'] = self.timestamp.isoformat()
        data['expires_at'] = self.expires_at.isoformat() if self.expires_at else None
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'A2AMessage':
        """Create from dictionary."""
        # Convert strings back to enums
        data['sender_type'] = AgentType(data['sender_type'])
        data['recipient_type'] = AgentType(data['recipient_type']) if data['recipient_type'] else None
        data['message_type'] = MessageType(data['message_type'])
        data['priority'] = Priority(data['priority'])
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['expires_at'] = datetime.fromisoformat(data['expires_at']) if data['expires_at'] else None
        return cls(**data)


class A2AAgent:
    """Base class dla agentów obsługujących protokół A2A."""
    
    def __init__(self, agent_id: str, agent_type: AgentType):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.message_handlers: Dict[str, Callable] = {}
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.is_active = True
        self.message_queue = asyncio.Queue()
        self.pending_requests: Dict[str, asyncio.Future] = {}
        
        # Register default handlers
        self._register_default_handlers()
        
        logger.info(f"A2A Agent initialized: {agent_id} ({agent_type.value})")
    
    def _register_default_handlers(self):
        """Register default message handlers."""
        self.register_handler("ping", self._handle_ping)
        self.register_handler("status", self._handle_status)
        self.register_handler("capabilities", self._handle_capabilities)
    
    def register_handler(self, action: str, handler: Callable):
        """Register message handler for specific action."""
        self.message_handlers[action] = handler
        logger.debug(f"Registered handler for action: {action}")
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register event handler."""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)
        logger.debug(f"Registered event handler for: {event_type}")
    
    async def send_message(self, message: A2AMessage, protocol_manager: 'EnhancedA2AProtocol') -> Optional[A2AMessage]:
        """Send message through protocol manager."""
        return await protocol_manager.send_message(message)
    
    async def send_request(self, recipient_id: str, recipient_type: AgentType, action: str, 
                          payload: Dict[str, Any], protocol_manager: 'EnhancedA2AProtocol',
                          priority: Priority = Priority.MEDIUM) -> Optional[A2AMessage]:
        """Send request and wait for response."""
        message = A2AMessage(
            id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            sender_type=self.agent_type,
            recipient_id=recipient_id,
            recipient_type=recipient_type,
            message_type=MessageType.REQUEST,
            priority=priority,
            payload={"action": action, **payload},
            timestamp=datetime.now()
        )
        
        return await protocol_manager.send_request(message)
    
    async def send_event(self, event_type: str, payload: Dict[str, Any], 
                        protocol_manager: 'EnhancedA2AProtocol',
                        priority: Priority = Priority.MEDIUM):
        """Send event to all interested agents."""
        message = A2AMessage(
            id=str(uuid.uuid4()),
            sender_id=self.agent_id,
            sender_type=self.agent_type,
            recipient_id=None,
            recipient_type=None,
            message_type=MessageType.EVENT,
            priority=priority,
            payload={"event_type": event_type, **payload},
            timestamp=datetime.now()
        )
        
        await protocol_manager.broadcast_event(message)
    
    async def handle_message(self, message: A2AMessage) -> Optional[A2AMessage]:
        """Handle incoming message."""
        try:
            action = message.payload.get("action")
            event_type = message.payload.get("event_type")
            
            if message.message_type == MessageType.REQUEST and action:
                if action in self.message_handlers:
                    response_payload = await self.message_handlers[action](message.payload)
                    
                    # Create response message
                    response = A2AMessage(
                        id=str(uuid.uuid4()),
                        sender_id=self.agent_id,
                        sender_type=self.agent_type,
                        recipient_id=message.sender_id,
                        recipient_type=message.sender_type,
                        message_type=MessageType.RESPONSE,
                        priority=message.priority,
                        payload=response_payload or {},
                        timestamp=datetime.now(),
                        correlation_id=message.id
                    )
                    
                    return response
                else:
                    logger.warning(f"No handler for action: {action}")
                    
            elif message.message_type == MessageType.EVENT and event_type:
                if event_type in self.event_handlers:
                    for handler in self.event_handlers[event_type]:
                        try:
                            await handler(message.payload)
                        except Exception as e:
                            logger.error(f"Event handler failed: {e}")
            
            elif message.message_type == MessageType.RESPONSE:
                # Handle response to our request
                correlation_id = message.correlation_id
                if correlation_id in self.pending_requests:
                    future = self.pending_requests.pop(correlation_id)
                    if not future.done():
                        future.set_result(message)
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to handle message: {e}")
            return None
    
    async def _handle_ping(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle ping request."""
        return {
            "status": "pong",
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _handle_status(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle status request."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "is_active": self.is_active,
            "queue_size": self.message_queue.qsize(),
            "pending_requests": len(self.pending_requests),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _handle_capabilities(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle capabilities request."""
        return {
            "agent_id": self.agent_id,
            "agent_type": self.agent_type.value,
            "supported_actions": list(self.message_handlers.keys()),
            "supported_events": list(self.event_handlers.keys()),
            "timestamp": datetime.now().isoformat()
        }


class CalendarManagerAgent(A2AAgent):
    """Calendar Manager Agent z obsługą A2A."""
    
    def __init__(self, agent_id: str = "calendar_manager"):
        super().__init__(agent_id, AgentType.CALENDAR_MANAGER)
        
        if CALENDAR_AVAILABLE:
            self.calendar_agent = AdvancedCalendarManagementAgent()
            self.csv_ingest = CSVCalendarIngest(self.calendar_agent)
        else:
            self.calendar_agent = None
            self.csv_ingest = None
        
        # Register calendar-specific handlers
        self.register_handler("optimize_schedule", self._handle_optimize_schedule)
        self.register_handler("add_order", self._handle_add_order)
        self.register_handler("add_technician", self._handle_add_technician)
        self.register_handler("get_schedule", self._handle_get_schedule)
        self.register_handler("import_csv", self._handle_import_csv)
        self.register_handler("export_csv", self._handle_export_csv)
        self.register_handler("get_statistics", self._handle_get_statistics)
        
        # Register event handlers
        self.register_event_handler("customer_analyzed", self._handle_customer_analyzed)
        self.register_event_handler("quote_generated", self._handle_quote_generated)
        self.register_event_handler("equipment_matched", self._handle_equipment_matched)
    
    async def _handle_optimize_schedule(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle schedule optimization request."""
        if not self.calendar_agent:
            return {"error": "Calendar agent not available"}
        
        try:
            target_date_str = payload.get("target_date")
            if target_date_str:
                target_date = datetime.fromisoformat(target_date_str)
            else:
                target_date = datetime.now() + timedelta(days=1)
            
            optimized_schedule = await self.calendar_agent.optimize_daily_schedule(target_date)
            stats = self.calendar_agent.get_optimization_statistics()
            
            return {
                "status": "success",
                "target_date": target_date.isoformat(),
                "schedule": self._serialize_schedule(optimized_schedule),
                "statistics": stats
            }
            
        except Exception as e:
            logger.error(f"Schedule optimization failed: {e}")
            return {"error": str(e)}
    
    async def _handle_import_csv(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle CSV import request."""
        if not self.csv_ingest:
            return {"error": "CSV ingest not available"}
        
        try:
            file_path = payload.get("file_path")
            data_type = payload.get("data_type", "auto")
            
            if not file_path:
                return {"error": "file_path required"}
            
            result = self.csv_ingest.import_from_csv(file_path, data_type)
            
            return {
                "status": "success",
                "success_count": result.success_count,
                "error_count": result.error_count,
                "warnings": result.warnings,
                "errors": result.errors,
                "imported_orders": result.imported_orders,
                "imported_technicians": result.imported_technicians
            }
            
        except Exception as e:
            logger.error(f"CSV import failed: {e}")
            return {"error": str(e)}
    
    def _serialize_schedule(self, schedule: Dict) -> Dict:
        """Serialize schedule for JSON transmission."""
        serialized = {}
        for tech_id, appointments in schedule.items():
            serialized[tech_id] = [
                {
                    "order_id": apt.order.id,
                    "customer_name": apt.order.customer_name,
                    "service_type": apt.order.service_type.value,
                    "start_time": apt.start_time.isoformat(),
                    "end_time": apt.end_time.isoformat(),
                    "address": apt.order.location.address,
                    "travel_time_before": str(apt.travel_time_before),
                    "travel_time_after": str(apt.travel_time_after)
                }
                for apt in appointments
            ]
        return serialized
    
    async def _handle_customer_analyzed(self, payload: Dict[str, Any]):
        """Handle customer analysis event."""
        logger.info(f"Customer analyzed: {payload.get('customer_name')}")
        # Could trigger automatic service order creation
    
    async def _handle_quote_generated(self, payload: Dict[str, Any]):
        """Handle quote generation event."""
        logger.info(f"Quote generated for: {payload.get('customer_name')}")
        # Could trigger installation scheduling

    async def _handle_add_order(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle add service order request."""
        if not self.calendar_agent:
            return {"error": "Calendar agent not available"}

        try:
            # Extract order data from payload
            order_data = payload.get("order_data", {})

            # This would create a ServiceOrder and add it
            # Implementation depends on the specific data format

            return {
                "status": "success",
                "message": "Service order added successfully"
            }

        except Exception as e:
            return {"error": str(e)}

    async def _handle_add_technician(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle add technician request."""
        if not self.calendar_agent:
            return {"error": "Calendar agent not available"}

        try:
            # Extract technician data from payload
            tech_data = payload.get("technician_data", {})

            # This would create a Technician and add it
            # Implementation depends on the specific data format

            return {
                "status": "success",
                "message": "Technician added successfully"
            }

        except Exception as e:
            return {"error": str(e)}

    async def _handle_get_schedule(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get schedule request."""
        if not self.calendar_agent:
            return {"error": "Calendar agent not available"}

        try:
            # Return current scheduled appointments
            appointments = self.calendar_agent.scheduled_appointments

            serialized_appointments = [
                {
                    "order_id": apt.order.id,
                    "technician_id": apt.technician.id,
                    "start_time": apt.start_time.isoformat(),
                    "end_time": apt.end_time.isoformat(),
                    "customer_name": apt.order.customer_name,
                    "service_type": apt.order.service_type.value
                }
                for apt in appointments
            ]

            return {
                "status": "success",
                "appointments": serialized_appointments,
                "count": len(appointments)
            }

        except Exception as e:
            return {"error": str(e)}

    async def _handle_export_csv(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle CSV export request."""
        if not self.csv_ingest:
            return {"error": "CSV ingest not available"}

        try:
            output_dir = payload.get("output_dir", "./exports")
            export_type = payload.get("export_type", "all")

            created_files = self.csv_ingest.export_to_csv(output_dir, export_type)

            return {
                "status": "success",
                "created_files": created_files
            }

        except Exception as e:
            return {"error": str(e)}

    async def _handle_get_statistics(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Handle get statistics request."""
        if not self.calendar_agent:
            return {"error": "Calendar agent not available"}

        try:
            stats = self.calendar_agent.get_optimization_statistics()
            return {
                "status": "success",
                "statistics": stats
            }

        except Exception as e:
            return {"error": str(e)}

    async def _handle_equipment_matched(self, payload: Dict[str, Any]):
        """Handle equipment matching event."""
        logger.info(f"Equipment matched: {payload.get('equipment_type')}")
        # Could trigger maintenance scheduling


class EnhancedA2AProtocol:
    """
    Enhanced Agent-to-Agent Protocol Manager.

    Manages communication between all agents in the HVAC CRM system.
    """

    def __init__(self):
        self.agents: Dict[str, A2AAgent] = {}
        self.message_queue = asyncio.Queue()
        self.event_subscriptions: Dict[str, List[str]] = {}  # event_type -> [agent_ids]
        self.message_history: List[A2AMessage] = []
        self.is_running = False
        self.stats = {
            "messages_sent": 0,
            "messages_received": 0,
            "events_broadcast": 0,
            "failed_deliveries": 0
        }

        logger.info("Enhanced A2A Protocol Manager initialized")

    def register_agent(self, agent: A2AAgent):
        """Register agent with the protocol manager."""
        self.agents[agent.agent_id] = agent
        logger.info(f"Registered agent: {agent.agent_id} ({agent.agent_type.value})")

    def unregister_agent(self, agent_id: str):
        """Unregister agent from the protocol manager."""
        if agent_id in self.agents:
            del self.agents[agent_id]
            logger.info(f"Unregistered agent: {agent_id}")

    def subscribe_to_event(self, agent_id: str, event_type: str):
        """Subscribe agent to specific event type."""
        if event_type not in self.event_subscriptions:
            self.event_subscriptions[event_type] = []

        if agent_id not in self.event_subscriptions[event_type]:
            self.event_subscriptions[event_type].append(agent_id)
            logger.debug(f"Agent {agent_id} subscribed to event: {event_type}")

    async def send_message(self, message: A2AMessage) -> bool:
        """Send message to specific recipient."""
        try:
            if message.recipient_id and message.recipient_id in self.agents:
                recipient = self.agents[message.recipient_id]
                await recipient.handle_message(message)

                self.stats["messages_sent"] += 1
                self.message_history.append(message)

                logger.debug(f"Message sent: {message.id} -> {message.recipient_id}")
                return True
            else:
                logger.warning(f"Recipient not found: {message.recipient_id}")
                self.stats["failed_deliveries"] += 1
                return False

        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            self.stats["failed_deliveries"] += 1
            return False

    async def send_request(self, message: A2AMessage) -> Optional[A2AMessage]:
        """Send request and wait for response."""
        try:
            if message.recipient_id and message.recipient_id in self.agents:
                recipient = self.agents[message.recipient_id]

                # Create future for response
                response_future = asyncio.Future()
                sender = self.agents.get(message.sender_id)
                if sender:
                    sender.pending_requests[message.id] = response_future

                # Send message
                response = await recipient.handle_message(message)

                if response:
                    # Direct response
                    if sender:
                        await sender.handle_message(response)

                    self.stats["messages_sent"] += 1
                    self.message_history.append(message)
                    self.message_history.append(response)

                    return response
                else:
                    # Wait for async response
                    try:
                        response = await asyncio.wait_for(response_future, timeout=30.0)
                        return response
                    except asyncio.TimeoutError:
                        logger.warning(f"Request timeout: {message.id}")
                        if sender and message.id in sender.pending_requests:
                            del sender.pending_requests[message.id]
                        return None
            else:
                logger.warning(f"Recipient not found: {message.recipient_id}")
                return None

        except Exception as e:
            logger.error(f"Failed to send request: {e}")
            return None

    async def broadcast_event(self, message: A2AMessage):
        """Broadcast event to all subscribed agents."""
        try:
            event_type = message.payload.get("event_type")
            if not event_type:
                logger.warning("Event message missing event_type")
                return

            subscribers = self.event_subscriptions.get(event_type, [])

            for agent_id in subscribers:
                if agent_id in self.agents:
                    agent = self.agents[agent_id]
                    try:
                        await agent.handle_message(message)
                    except Exception as e:
                        logger.error(f"Failed to deliver event to {agent_id}: {e}")

            self.stats["events_broadcast"] += 1
            self.message_history.append(message)

            logger.debug(f"Event broadcast: {event_type} to {len(subscribers)} agents")

        except Exception as e:
            logger.error(f"Failed to broadcast event: {e}")

    async def start(self):
        """Start the protocol manager."""
        self.is_running = True
        logger.info("A2A Protocol Manager started")

        # Start message processing loop
        asyncio.create_task(self._message_processing_loop())

    async def stop(self):
        """Stop the protocol manager."""
        self.is_running = False
        logger.info("A2A Protocol Manager stopped")

    async def _message_processing_loop(self):
        """Main message processing loop."""
        while self.is_running:
            try:
                # Process any queued messages
                if not self.message_queue.empty():
                    message = await self.message_queue.get()
                    await self.send_message(message)

                # Small delay to prevent busy waiting
                await asyncio.sleep(0.01)

            except Exception as e:
                logger.error(f"Message processing error: {e}")
                await asyncio.sleep(1)

    def get_statistics(self) -> Dict[str, Any]:
        """Get protocol statistics."""
        return {
            "registered_agents": len(self.agents),
            "active_agents": sum(1 for agent in self.agents.values() if agent.is_active),
            "event_subscriptions": len(self.event_subscriptions),
            "message_history_size": len(self.message_history),
            "stats": self.stats.copy(),
            "agents": {
                agent_id: {
                    "type": agent.agent_type.value,
                    "is_active": agent.is_active,
                    "queue_size": agent.message_queue.qsize(),
                    "pending_requests": len(agent.pending_requests)
                }
                for agent_id, agent in self.agents.items()
            }
        }

    def get_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Get capabilities of all registered agents."""
        capabilities = {}

        for agent_id, agent in self.agents.items():
            capabilities[agent_id] = {
                "agent_type": agent.agent_type.value,
                "supported_actions": list(agent.message_handlers.keys()),
                "supported_events": list(agent.event_handlers.keys()),
                "is_active": agent.is_active
            }

        return capabilities


# Factory function for creating agents
def create_calendar_manager_agent(agent_id: str = "calendar_manager") -> CalendarManagerAgent:
    """Create and configure Calendar Manager Agent."""
    return CalendarManagerAgent(agent_id)


def create_a2a_protocol_with_calendar() -> tuple[EnhancedA2AProtocol, CalendarManagerAgent]:
    """Create A2A protocol with Calendar Manager Agent."""
    protocol = EnhancedA2AProtocol()
    calendar_agent = create_calendar_manager_agent()

    # Register agent
    protocol.register_agent(calendar_agent)

    # Subscribe to relevant events
    protocol.subscribe_to_event(calendar_agent.agent_id, "customer_analyzed")
    protocol.subscribe_to_event(calendar_agent.agent_id, "quote_generated")
    protocol.subscribe_to_event(calendar_agent.agent_id, "equipment_matched")
    protocol.subscribe_to_event(calendar_agent.agent_id, "maintenance_required")

    logger.info("A2A Protocol with Calendar Manager created successfully")

    return protocol, calendar_agent
