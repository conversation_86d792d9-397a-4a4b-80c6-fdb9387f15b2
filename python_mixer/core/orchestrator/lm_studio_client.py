#!/usr/bin/env python3
"""
LM Studio Client for HVAC Python Mixer
Integration with local LM Studio instance running Gemma3-4b model
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from dataclasses import dataclass, asdict

import httpx
from loguru import logger


@dataclass
class LMStudioConfig:
    """LM Studio configuration."""
    base_url: str
    model_name: str
    max_tokens: int
    temperature: float
    timeout: float
    retry_attempts: int
    connection_pool_size: int


@dataclass
class ChatMessage:
    """Chat message structure."""
    role: str  # system, user, assistant
    content: str
    timestamp: Optional[datetime] = None


@dataclass
class CompletionRequest:
    """Completion request structure."""
    messages: List[ChatMessage]
    max_tokens: Optional[int] = None
    temperature: Optional[float] = None
    stream: bool = False
    stop: Optional[List[str]] = None


@dataclass
class CompletionResponse:
    """Completion response structure."""
    content: str
    model: str
    usage: Dict[str, int]
    finish_reason: str
    response_time: float
    timestamp: datetime


class LMStudioClient:
    """
    LM Studio Client for AI-powered HVAC analysis.
    
    Provides:
    - Connection to local LM Studio at http://192.168.0.179:1234
    - Gemma3-4b model integration for HVAC-specific tasks
    - Connection pooling and retry logic
    - Streaming and non-streaming completions
    - HVAC domain-specific prompt templates
    - Performance monitoring and optimization
    """
    
    def __init__(
        self, 
        base_url: str = "http://192.168.0.179:1234",
        model_name: str = "gemma3-4b",
        max_tokens: int = 2048,
        temperature: float = 0.7,
        timeout: float = 60.0,
        retry_attempts: int = 3
    ):
        self.config = LMStudioConfig(
            base_url=base_url,
            model_name=model_name,
            max_tokens=max_tokens,
            temperature=temperature,
            timeout=timeout,
            retry_attempts=retry_attempts,
            connection_pool_size=10
        )
        
        # HTTP client with connection pooling
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(timeout),
            limits=httpx.Limits(
                max_keepalive_connections=self.config.connection_pool_size,
                max_connections=self.config.connection_pool_size * 2
            )
        )
        
        # Connection status
        self.is_connected = False
        self.last_health_check = None
        
        # Performance metrics
        self.request_count = 0
        self.total_response_time = 0.0
        self.error_count = 0
        self.token_usage = {"prompt_tokens": 0, "completion_tokens": 0}
        
        # HVAC-specific prompt templates
        self.hvac_prompts = {
            "equipment_analysis": """
You are an expert HVAC technician and engineer. Analyze the following equipment information and provide detailed insights.

Equipment Details: {equipment_info}

Please provide:
1. Equipment identification and specifications
2. Common issues and troubleshooting steps
3. Maintenance recommendations
4. Energy efficiency assessment
5. Replacement or upgrade suggestions

Format your response as structured JSON with clear sections.
""",
            
            "customer_email_analysis": """
You are an expert HVAC customer service representative. Analyze the following customer email and provide actionable insights.

Customer Email: {email_content}

Please provide:
1. Customer intent classification (service, quote, complaint, etc.)
2. Urgency level (low, medium, high, urgent)
3. Equipment mentioned and potential issues
4. Recommended response actions
5. Estimated service value and timeline

Format your response as structured JSON for easy processing.
""",
            
            "service_optimization": """
You are an expert HVAC operations manager. Analyze the following service data and provide optimization recommendations.

Service Data: {service_data}

Please provide:
1. Route optimization suggestions
2. Technician skill matching
3. Parts and inventory recommendations
4. Scheduling optimization
5. Customer satisfaction improvements

Focus on practical, actionable recommendations that improve efficiency and customer satisfaction.
""",
            
            "predictive_maintenance": """
You are an expert HVAC predictive maintenance specialist. Analyze the following equipment data and provide maintenance predictions.

Equipment Data: {equipment_data}

Please provide:
1. Failure risk assessment
2. Recommended maintenance schedule
3. Parts replacement timeline
4. Performance optimization suggestions
5. Cost-benefit analysis

Base your recommendations on industry best practices and equipment specifications.
"""
        }
    
    async def initialize(self) -> bool:
        """Initialize LM Studio client and test connection."""
        try:
            logger.info(f"🚀 Initializing LM Studio Client")
            logger.info(f"🔗 Base URL: {self.config.base_url}")
            logger.info(f"🤖 Model: {self.config.model_name}")
            
            # Test connection
            health_check = await self.health_check()
            
            if health_check["status"] == "healthy":
                self.is_connected = True
                logger.success("✅ LM Studio connection established")
                logger.info(f"📊 Model info: {health_check.get('model_info', 'Unknown')}")
                return True
            else:
                logger.warning("⚠️ LM Studio connection failed, operating in fallback mode")
                self.is_connected = False
                return False
                
        except Exception as e:
            logger.error(f"❌ LM Studio initialization failed: {e}")
            self.is_connected = False
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """Check LM Studio server health."""
        try:
            response = await self.client.get(f"{self.config.base_url}/v1/models")
            
            if response.status_code == 200:
                models_data = response.json()
                self.last_health_check = datetime.now()
                
                return {
                    "status": "healthy",
                    "timestamp": self.last_health_check.isoformat(),
                    "models": models_data.get("data", []),
                    "model_info": f"Available models: {len(models_data.get('data', []))}"
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": f"HTTP {response.status_code}",
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def complete(
        self, 
        messages: List[ChatMessage], 
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        stream: bool = False
    ) -> CompletionResponse:
        """Generate completion using LM Studio."""
        if not self.is_connected:
            raise ConnectionError("LM Studio not connected")
        
        start_time = time.time()
        
        try:
            # Prepare request
            request_data = {
                "model": self.config.model_name,
                "messages": [
                    {"role": msg.role, "content": msg.content} 
                    for msg in messages
                ],
                "max_tokens": max_tokens or self.config.max_tokens,
                "temperature": temperature or self.config.temperature,
                "stream": stream
            }
            
            # Make request with retry logic
            response = await self._make_request_with_retry(
                "POST", 
                f"{self.config.base_url}/v1/chat/completions",
                json=request_data
            )
            
            response_time = time.time() - start_time
            
            # Parse response
            response_data = response.json()
            
            if "choices" in response_data and response_data["choices"]:
                choice = response_data["choices"][0]
                content = choice["message"]["content"]
                finish_reason = choice.get("finish_reason", "unknown")
            else:
                raise ValueError("Invalid response format from LM Studio")
            
            # Update metrics
            self.request_count += 1
            self.total_response_time += response_time
            
            if "usage" in response_data:
                usage = response_data["usage"]
                self.token_usage["prompt_tokens"] += usage.get("prompt_tokens", 0)
                self.token_usage["completion_tokens"] += usage.get("completion_tokens", 0)
            
            completion_response = CompletionResponse(
                content=content,
                model=self.config.model_name,
                usage=response_data.get("usage", {}),
                finish_reason=finish_reason,
                response_time=response_time,
                timestamp=datetime.now()
            )
            
            logger.debug(f"✅ Completion generated in {response_time:.2f}s")
            
            return completion_response
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ Completion failed: {e}")
            raise
    
    async def complete_streaming(
        self, 
        messages: List[ChatMessage],
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None
    ) -> AsyncGenerator[str, None]:
        """Generate streaming completion."""
        if not self.is_connected:
            raise ConnectionError("LM Studio not connected")
        
        try:
            request_data = {
                "model": self.config.model_name,
                "messages": [
                    {"role": msg.role, "content": msg.content} 
                    for msg in messages
                ],
                "max_tokens": max_tokens or self.config.max_tokens,
                "temperature": temperature or self.config.temperature,
                "stream": True
            }
            
            async with self.client.stream(
                "POST",
                f"{self.config.base_url}/v1/chat/completions",
                json=request_data
            ) as response:
                
                if response.status_code != 200:
                    raise httpx.HTTPStatusError(
                        f"HTTP {response.status_code}", 
                        request=response.request, 
                        response=response
                    )
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and data["choices"]:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            self.error_count += 1
            logger.error(f"❌ Streaming completion failed: {e}")
            raise
    
    async def analyze_hvac_equipment(self, equipment_info: str) -> Dict[str, Any]:
        """Analyze HVAC equipment using specialized prompt."""
        try:
            prompt = self.hvac_prompts["equipment_analysis"].format(
                equipment_info=equipment_info
            )
            
            messages = [
                ChatMessage(role="system", content="You are an expert HVAC technician and engineer."),
                ChatMessage(role="user", content=prompt)
            ]
            
            response = await self.complete(messages, temperature=0.3)
            
            # Try to parse JSON response
            try:
                analysis = json.loads(response.content)
            except json.JSONDecodeError:
                # Fallback to text response
                analysis = {"analysis": response.content, "format": "text"}
            
            return {
                "success": True,
                "analysis": analysis,
                "response_time": response.response_time,
                "model": response.model
            }
            
        except Exception as e:
            logger.error(f"Equipment analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis": None
            }
    
    async def analyze_customer_email(self, email_content: str) -> Dict[str, Any]:
        """Analyze customer email using specialized prompt."""
        try:
            prompt = self.hvac_prompts["customer_email_analysis"].format(
                email_content=email_content
            )
            
            messages = [
                ChatMessage(role="system", content="You are an expert HVAC customer service representative."),
                ChatMessage(role="user", content=prompt)
            ]
            
            response = await self.complete(messages, temperature=0.2)
            
            # Try to parse JSON response
            try:
                analysis = json.loads(response.content)
            except json.JSONDecodeError:
                analysis = {"analysis": response.content, "format": "text"}
            
            return {
                "success": True,
                "analysis": analysis,
                "response_time": response.response_time,
                "model": response.model
            }
            
        except Exception as e:
            logger.error(f"Email analysis failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "analysis": None
            }
    
    async def optimize_service_operations(self, service_data: str) -> Dict[str, Any]:
        """Optimize service operations using AI analysis."""
        try:
            prompt = self.hvac_prompts["service_optimization"].format(
                service_data=service_data
            )
            
            messages = [
                ChatMessage(role="system", content="You are an expert HVAC operations manager."),
                ChatMessage(role="user", content=prompt)
            ]
            
            response = await self.complete(messages, temperature=0.4)
            
            try:
                optimization = json.loads(response.content)
            except json.JSONDecodeError:
                optimization = {"recommendations": response.content, "format": "text"}
            
            return {
                "success": True,
                "optimization": optimization,
                "response_time": response.response_time,
                "model": response.model
            }
            
        except Exception as e:
            logger.error(f"Service optimization failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "optimization": None
            }
    
    async def predict_maintenance(self, equipment_data: str) -> Dict[str, Any]:
        """Predict maintenance needs using AI analysis."""
        try:
            prompt = self.hvac_prompts["predictive_maintenance"].format(
                equipment_data=equipment_data
            )
            
            messages = [
                ChatMessage(role="system", content="You are an expert HVAC predictive maintenance specialist."),
                ChatMessage(role="user", content=prompt)
            ]
            
            response = await self.complete(messages, temperature=0.3)
            
            try:
                prediction = json.loads(response.content)
            except json.JSONDecodeError:
                prediction = {"prediction": response.content, "format": "text"}
            
            return {
                "success": True,
                "prediction": prediction,
                "response_time": response.response_time,
                "model": response.model
            }
            
        except Exception as e:
            logger.error(f"Maintenance prediction failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "prediction": None
            }
    
    async def _make_request_with_retry(
        self, 
        method: str, 
        url: str, 
        **kwargs
    ) -> httpx.Response:
        """Make HTTP request with retry logic."""
        last_exception = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                response = await self.client.request(method, url, **kwargs)
                
                if response.status_code == 200:
                    return response
                else:
                    raise httpx.HTTPStatusError(
                        f"HTTP {response.status_code}",
                        request=response.request,
                        response=response
                    )
                    
            except Exception as e:
                last_exception = e
                if attempt < self.config.retry_attempts - 1:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.warning(f"Request failed (attempt {attempt + 1}), retrying in {wait_time}s...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error(f"Request failed after {self.config.retry_attempts} attempts")
        
        raise last_exception
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get LM Studio client performance metrics."""
        avg_response_time = 0.0
        if self.request_count > 0:
            avg_response_time = self.total_response_time / self.request_count
        
        error_rate = 0.0
        if self.request_count > 0:
            error_rate = (self.error_count / self.request_count) * 100
        
        return {
            "connection_status": "connected" if self.is_connected else "disconnected",
            "base_url": self.config.base_url,
            "model_name": self.config.model_name,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": f"{error_rate:.1f}%",
            "average_response_time": f"{avg_response_time:.2f}s",
            "total_tokens_used": self.token_usage["prompt_tokens"] + self.token_usage["completion_tokens"],
            "prompt_tokens": self.token_usage["prompt_tokens"],
            "completion_tokens": self.token_usage["completion_tokens"],
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None
        }
    
    async def close(self):
        """Close LM Studio client."""
        await self.client.aclose()
        logger.info("LM Studio client closed")
