#!/usr/bin/env python3
"""
MCP Memory Client for HVAC Python Mixer
Persistent project state and development progress tracking
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path

from loguru import logger
import httpx


@dataclass
class ProjectState:
    """Project state data structure."""
    session_id: str
    timestamp: datetime
    phase: str
    components_status: Dict[str, str]
    performance_metrics: Dict[str, float]
    development_progress: Dict[str, Any]
    user_context: Dict[str, Any]
    errors: List[str]
    achievements: List[str]


@dataclass
class DevelopmentSession:
    """Development session tracking."""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime]
    phase: str
    tasks_completed: List[str]
    tasks_pending: List[str]
    performance_data: Dict[str, Any]
    notes: str


class MemoryClient:
    """
    MCP Memory Client for persistent state management.
    
    Provides:
    - Project state persistence across sessions
    - Development progress tracking
    - Performance metrics storage
    - Context maintenance for HVAC CRM development
    """
    
    def __init__(self, memory_server_url: str = "http://localhost:3001"):
        self.memory_server_url = memory_server_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.current_session: Optional[DevelopmentSession] = None
        self.project_state: Optional[ProjectState] = None
        
        # Local cache for offline operation
        self.cache_dir = Path(__file__).parent.parent.parent / ".mcp_cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.cache_file = self.cache_dir / "memory_cache.json"
        
        # Performance tracking
        self.operation_times = []
        
    async def initialize(self) -> bool:
        """Initialize MCP Memory client."""
        try:
            logger.info("Initializing MCP Memory Client...")
            
            # Test connection to memory server
            response = await self.client.get(f"{self.memory_server_url}/health")
            if response.status_code == 200:
                logger.success("MCP Memory server connected successfully")
                
                # Load existing project state
                await self.load_project_state()
                
                # Start new development session
                await self.start_development_session()
                
                return True
            else:
                logger.warning("MCP Memory server not available, using local cache")
                await self.load_from_cache()
                return True
                
        except Exception as e:
            logger.warning(f"MCP Memory initialization failed: {e}")
            logger.info("Falling back to local cache mode")
            await self.load_from_cache()
            return True
    
    async def start_development_session(self) -> str:
        """Start a new development session."""
        session_id = f"hvac_session_{int(time.time())}"
        
        self.current_session = DevelopmentSession(
            session_id=session_id,
            start_time=datetime.now(),
            end_time=None,
            phase="Phase 1: Foundation Enhancement",
            tasks_completed=[],
            tasks_pending=[
                "UV Virtual Environment Setup",
                "MCP Memory Integration", 
                "Enhanced CSV Data Processing",
                "Tavily Research Integration",
                "LM Studio Integration",
                "Main Orchestrator Implementation",
                "Calendar Management Agent Testing"
            ],
            performance_data={},
            notes="Starting comprehensive python_mixer enhancement project"
        )
        
        logger.info(f"Started development session: {session_id}")
        await self.save_session()
        
        return session_id
    
    async def update_task_progress(self, task: str, status: str, notes: str = "") -> bool:
        """Update task progress in current session."""
        if not self.current_session:
            logger.warning("No active session for task update")
            return False
        
        try:
            if status == "completed":
                if task in self.current_session.tasks_pending:
                    self.current_session.tasks_pending.remove(task)
                if task not in self.current_session.tasks_completed:
                    self.current_session.tasks_completed.append(task)
                    
            elif status == "pending":
                if task not in self.current_session.tasks_pending:
                    self.current_session.tasks_pending.append(task)
                    
            # Add notes
            if notes:
                self.current_session.notes += f"\n[{datetime.now().isoformat()}] {task}: {notes}"
            
            await self.save_session()
            logger.info(f"Task progress updated: {task} -> {status}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update task progress: {e}")
            return False
    
    async def save_project_state(self, state_data: Dict[str, Any]) -> bool:
        """Save current project state to memory."""
        try:
            self.project_state = ProjectState(
                session_id=self.current_session.session_id if self.current_session else "unknown",
                timestamp=datetime.now(),
                phase=state_data.get("phase", "Unknown"),
                components_status=state_data.get("components_status", {}),
                performance_metrics=state_data.get("performance_metrics", {}),
                development_progress=state_data.get("development_progress", {}),
                user_context=state_data.get("user_context", {}),
                errors=state_data.get("errors", []),
                achievements=state_data.get("achievements", [])
            )
            
            # Try to save to MCP server
            try:
                payload = {
                    "type": "project_state",
                    "data": asdict(self.project_state)
                }
                
                response = await self.client.post(
                    f"{self.memory_server_url}/memory/store",
                    json=payload
                )
                
                if response.status_code == 200:
                    logger.success("Project state saved to MCP Memory server")
                else:
                    logger.warning("Failed to save to MCP server, using local cache")
                    await self.save_to_cache()
                    
            except Exception as e:
                logger.warning(f"MCP server save failed: {e}, using local cache")
                await self.save_to_cache()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save project state: {e}")
            return False
    
    async def load_project_state(self) -> Optional[ProjectState]:
        """Load project state from memory."""
        try:
            # Try to load from MCP server
            response = await self.client.get(
                f"{self.memory_server_url}/memory/retrieve",
                params={"type": "project_state", "latest": True}
            )
            
            if response.status_code == 200:
                data = response.json()
                if data and "data" in data:
                    state_dict = data["data"]
                    # Convert timestamp string back to datetime
                    state_dict["timestamp"] = datetime.fromisoformat(state_dict["timestamp"])
                    self.project_state = ProjectState(**state_dict)
                    logger.success("Project state loaded from MCP Memory server")
                    return self.project_state
            
            # Fallback to local cache
            logger.info("Loading project state from local cache")
            return await self.load_from_cache()
            
        except Exception as e:
            logger.warning(f"Failed to load project state: {e}")
            return await self.load_from_cache()
    
    async def save_session(self) -> bool:
        """Save current development session."""
        if not self.current_session:
            return False
        
        try:
            # Try to save to MCP server
            payload = {
                "type": "development_session",
                "data": asdict(self.current_session)
            }
            
            response = await self.client.post(
                f"{self.memory_server_url}/memory/store",
                json=payload
            )
            
            if response.status_code == 200:
                logger.debug("Session saved to MCP Memory server")
            else:
                await self.save_to_cache()
                
            return True
            
        except Exception as e:
            logger.warning(f"Session save failed: {e}")
            await self.save_to_cache()
            return True
    
    async def save_to_cache(self) -> bool:
        """Save data to local cache."""
        try:
            cache_data = {
                "project_state": asdict(self.project_state) if self.project_state else None,
                "current_session": asdict(self.current_session) if self.current_session else None,
                "timestamp": datetime.now().isoformat()
            }
            
            # Convert datetime objects to strings for JSON serialization
            if cache_data["project_state"]:
                cache_data["project_state"]["timestamp"] = cache_data["project_state"]["timestamp"].isoformat()
            
            if cache_data["current_session"]:
                cache_data["current_session"]["start_time"] = cache_data["current_session"]["start_time"].isoformat()
                if cache_data["current_session"]["end_time"]:
                    cache_data["current_session"]["end_time"] = cache_data["current_session"]["end_time"].isoformat()
            
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
            
            logger.debug("Data saved to local cache")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save to cache: {e}")
            return False
    
    async def load_from_cache(self) -> Optional[ProjectState]:
        """Load data from local cache."""
        try:
            if not self.cache_file.exists():
                logger.info("No cache file found, starting fresh")
                return None
            
            with open(self.cache_file, 'r') as f:
                cache_data = json.load(f)
            
            # Load project state
            if cache_data.get("project_state"):
                state_dict = cache_data["project_state"]
                state_dict["timestamp"] = datetime.fromisoformat(state_dict["timestamp"])
                self.project_state = ProjectState(**state_dict)
            
            # Load current session
            if cache_data.get("current_session"):
                session_dict = cache_data["current_session"]
                session_dict["start_time"] = datetime.fromisoformat(session_dict["start_time"])
                if session_dict["end_time"]:
                    session_dict["end_time"] = datetime.fromisoformat(session_dict["end_time"])
                self.current_session = DevelopmentSession(**session_dict)
            
            logger.info("Data loaded from local cache")
            return self.project_state
            
        except Exception as e:
            logger.error(f"Failed to load from cache: {e}")
            return None
    
    async def get_development_summary(self) -> Dict[str, Any]:
        """Get comprehensive development summary."""
        if not self.current_session:
            return {"error": "No active session"}
        
        total_tasks = len(self.current_session.tasks_completed) + len(self.current_session.tasks_pending)
        completion_rate = len(self.current_session.tasks_completed) / total_tasks if total_tasks > 0 else 0
        
        session_duration = datetime.now() - self.current_session.start_time
        
        return {
            "session_id": self.current_session.session_id,
            "phase": self.current_session.phase,
            "completion_rate": completion_rate,
            "tasks_completed": len(self.current_session.tasks_completed),
            "tasks_pending": len(self.current_session.tasks_pending),
            "session_duration": str(session_duration),
            "recent_achievements": self.current_session.tasks_completed[-3:] if self.current_session.tasks_completed else [],
            "next_tasks": self.current_session.tasks_pending[:3] if self.current_session.tasks_pending else [],
            "performance_data": self.current_session.performance_data
        }
    
    async def close(self):
        """Close MCP Memory client."""
        if self.current_session:
            self.current_session.end_time = datetime.now()
            await self.save_session()
        
        await self.save_to_cache()
        await self.client.aclose()
        logger.info("MCP Memory client closed")
