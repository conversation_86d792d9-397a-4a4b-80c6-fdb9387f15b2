#!/usr/bin/env python3
"""
Batch Processor for HVAC Python Mixer
High-performance batch processing for large HVAC datasets
"""

import asyncio
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, asdict
import json
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

from loguru import logger
import pandas as pd

from .enhanced_csv_processor import EnhancedCSVProcessor, ProcessingResult
from .data_validator import DataValidator


@dataclass
class BatchJob:
    """Batch processing job definition."""
    job_id: str
    files: List[Path]
    data_type: str
    priority: int  # 1-10, higher is more priority
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: str = "pending"  # pending, running, completed, failed
    results: List[ProcessingResult] = None
    error_message: Optional[str] = None


@dataclass
class BatchStats:
    """Batch processing statistics."""
    total_jobs: int
    completed_jobs: int
    failed_jobs: int
    total_files: int
    total_rows: int
    total_processing_time: float
    average_throughput: float
    memory_peak: float


class BatchProcessor:
    """
    High-performance batch processor for HVAC data.
    
    Features:
    - Parallel processing of multiple CSV files
    - Job queue management with priority
    - Progress tracking and monitoring
    - Memory optimization for large datasets
    - Integration with enhanced CSV processor
    - Automatic retry on failures
    """
    
    def __init__(
        self, 
        max_workers: int = None,
        use_multiprocessing: bool = True,
        chunk_size: int = 1000,
        max_memory_mb: int = 1000
    ):
        self.max_workers = max_workers or min(mp.cpu_count(), 8)
        self.use_multiprocessing = use_multiprocessing
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        
        # Job management
        self.job_queue: List[BatchJob] = []
        self.active_jobs: Dict[str, BatchJob] = {}
        self.completed_jobs: List[BatchJob] = []
        
        # Processing components
        self.csv_processor = EnhancedCSVProcessor(chunk_size, max_memory_mb)
        self.validator = DataValidator()
        
        # Statistics
        self.stats = BatchStats(0, 0, 0, 0, 0, 0.0, 0.0, 0.0)
        
        # Executor
        self.executor = None
        
        # Progress callbacks
        self.progress_callbacks: List[Callable] = []
        
    async def initialize(self) -> bool:
        """Initialize batch processor."""
        try:
            logger.info(f"🚀 Initializing Batch Processor")
            logger.info(f"⚙️ Max workers: {self.max_workers}")
            logger.info(f"🔧 Multiprocessing: {self.use_multiprocessing}")
            logger.info(f"📦 Chunk size: {self.chunk_size}")
            logger.info(f"💾 Memory limit: {self.max_memory_mb}MB")
            
            # Initialize executor
            if self.use_multiprocessing:
                self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
            else:
                self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
            
            logger.success("✅ Batch Processor initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Batch Processor initialization failed: {e}")
            return False
    
    async def submit_batch_job(
        self, 
        files: List[Union[str, Path]], 
        data_type: str = "service_orders",
        priority: int = 5
    ) -> str:
        """Submit a new batch processing job."""
        try:
            # Convert to Path objects
            file_paths = [Path(f) for f in files]
            
            # Validate files exist
            missing_files = [f for f in file_paths if not f.exists()]
            if missing_files:
                raise FileNotFoundError(f"Files not found: {missing_files}")
            
            # Create job
            job_id = f"batch_{int(time.time())}_{len(self.job_queue)}"
            job = BatchJob(
                job_id=job_id,
                files=file_paths,
                data_type=data_type,
                priority=priority,
                created_at=datetime.now(),
                results=[]
            )
            
            # Add to queue (sorted by priority)
            self.job_queue.append(job)
            self.job_queue.sort(key=lambda x: x.priority, reverse=True)
            
            self.stats.total_jobs += 1
            self.stats.total_files += len(file_paths)
            
            logger.info(f"📋 Batch job submitted: {job_id}")
            logger.info(f"📁 Files: {len(file_paths)}")
            logger.info(f"🎯 Data type: {data_type}")
            logger.info(f"⭐ Priority: {priority}")
            
            return job_id
            
        except Exception as e:
            logger.error(f"❌ Failed to submit batch job: {e}")
            raise
    
    async def process_batch_jobs(self) -> Dict[str, Any]:
        """Process all pending batch jobs."""
        if not self.job_queue:
            logger.info("📭 No pending batch jobs")
            return {"message": "No jobs to process"}
        
        logger.info(f"🔄 Processing {len(self.job_queue)} batch jobs")
        
        start_time = time.time()
        processed_jobs = []
        
        try:
            # Process jobs in priority order
            while self.job_queue:
                job = self.job_queue.pop(0)
                
                logger.info(f"🚀 Starting job: {job.job_id}")
                
                # Move to active jobs
                self.active_jobs[job.job_id] = job
                job.status = "running"
                job.started_at = datetime.now()
                
                # Process job
                try:
                    job_result = await self._process_single_job(job)
                    job.status = "completed"
                    job.completed_at = datetime.now()
                    self.stats.completed_jobs += 1
                    
                    logger.success(f"✅ Job completed: {job.job_id}")
                    
                except Exception as e:
                    job.status = "failed"
                    job.error_message = str(e)
                    job.completed_at = datetime.now()
                    self.stats.failed_jobs += 1
                    
                    logger.error(f"❌ Job failed: {job.job_id} - {e}")
                
                # Move to completed jobs
                self.completed_jobs.append(job)
                del self.active_jobs[job.job_id]
                processed_jobs.append(job)
                
                # Notify progress callbacks
                await self._notify_progress_callbacks(job)
            
            total_time = time.time() - start_time
            self.stats.total_processing_time += total_time
            
            # Calculate throughput
            total_rows = sum(
                sum(result.stats.total_rows for result in job.results) 
                for job in processed_jobs if job.results
            )
            self.stats.total_rows += total_rows
            
            if total_time > 0:
                self.stats.average_throughput = total_rows / total_time
            
            logger.success(f"🎉 Batch processing completed!")
            logger.info(f"⏱️ Total time: {total_time:.2f}s")
            logger.info(f"📊 Total rows: {total_rows}")
            logger.info(f"⚡ Throughput: {self.stats.average_throughput:.1f} rows/s")
            
            return {
                "success": True,
                "processed_jobs": len(processed_jobs),
                "total_time": total_time,
                "total_rows": total_rows,
                "throughput": self.stats.average_throughput,
                "job_results": [
                    {
                        "job_id": job.job_id,
                        "status": job.status,
                        "files_processed": len(job.files),
                        "total_rows": sum(r.stats.total_rows for r in job.results) if job.results else 0,
                        "error_message": job.error_message
                    }
                    for job in processed_jobs
                ]
            }
            
        except Exception as e:
            logger.error(f"❌ Batch processing failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _process_single_job(self, job: BatchJob) -> Dict[str, Any]:
        """Process a single batch job."""
        logger.info(f"📁 Processing {len(job.files)} files for job {job.job_id}")
        
        job_start_time = time.time()
        
        # Process files in parallel
        if self.use_multiprocessing and len(job.files) > 1:
            # Use multiprocessing for multiple files
            results = await self._process_files_parallel(job.files, job.data_type)
        else:
            # Sequential processing for single file or threading
            results = await self._process_files_sequential(job.files, job.data_type)
        
        job.results = results
        
        # Calculate job statistics
        total_rows = sum(result.stats.total_rows for result in results)
        successful_files = sum(1 for result in results if result.success)
        failed_files = len(results) - successful_files
        
        job_time = time.time() - job_start_time
        
        logger.info(f"📊 Job {job.job_id} summary:")
        logger.info(f"   ✅ Successful files: {successful_files}/{len(job.files)}")
        logger.info(f"   ❌ Failed files: {failed_files}")
        logger.info(f"   📈 Total rows: {total_rows}")
        logger.info(f"   ⏱️ Processing time: {job_time:.2f}s")
        
        return {
            "job_id": job.job_id,
            "total_files": len(job.files),
            "successful_files": successful_files,
            "failed_files": failed_files,
            "total_rows": total_rows,
            "processing_time": job_time
        }
    
    async def _process_files_parallel(
        self, 
        files: List[Path], 
        data_type: str
    ) -> List[ProcessingResult]:
        """Process files in parallel using multiprocessing."""
        logger.info(f"🔄 Processing {len(files)} files in parallel")
        
        try:
            # Create tasks for parallel processing
            loop = asyncio.get_event_loop()
            
            # Process files in batches to avoid overwhelming the system
            batch_size = min(self.max_workers, len(files))
            results = []
            
            for i in range(0, len(files), batch_size):
                batch_files = files[i:i + batch_size]
                
                # Submit batch to executor
                tasks = []
                for file_path in batch_files:
                    task = loop.run_in_executor(
                        self.executor,
                        self._process_file_sync,
                        file_path,
                        data_type
                    )
                    tasks.append(task)
                
                # Wait for batch completion
                batch_results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Handle results and exceptions
                for result in batch_results:
                    if isinstance(result, Exception):
                        logger.error(f"File processing error: {result}")
                        # Create error result
                        error_result = ProcessingResult(
                            success=False,
                            stats=None,
                            valid_data=[],
                            errors=[{"error": str(result)}],
                            warnings=[],
                            summary=f"Processing failed: {result}"
                        )
                        results.append(error_result)
                    else:
                        results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Parallel processing failed: {e}")
            raise
    
    async def _process_files_sequential(
        self, 
        files: List[Path], 
        data_type: str
    ) -> List[ProcessingResult]:
        """Process files sequentially."""
        logger.info(f"🔄 Processing {len(files)} files sequentially")
        
        results = []
        
        for file_path in files:
            try:
                result = await self.csv_processor.process_csv_file(
                    file_path, data_type
                )
                results.append(result)
                
            except Exception as e:
                logger.error(f"File processing failed for {file_path}: {e}")
                error_result = ProcessingResult(
                    success=False,
                    stats=None,
                    valid_data=[],
                    errors=[{"error": str(e), "file": str(file_path)}],
                    warnings=[],
                    summary=f"Processing failed: {e}"
                )
                results.append(error_result)
        
        return results
    
    def _process_file_sync(self, file_path: Path, data_type: str) -> ProcessingResult:
        """Synchronous file processing for multiprocessing."""
        try:
            # Create new processor instance for multiprocessing
            processor = EnhancedCSVProcessor(self.chunk_size, self.max_memory_mb)
            
            # Use asyncio.run for async processing in subprocess
            return asyncio.run(processor.process_csv_file(file_path, data_type))
            
        except Exception as e:
            logger.error(f"Sync file processing failed: {e}")
            raise
    
    async def _notify_progress_callbacks(self, job: BatchJob):
        """Notify progress callbacks about job completion."""
        for callback in self.progress_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(job)
                else:
                    callback(job)
            except Exception as e:
                logger.error(f"Progress callback failed: {e}")
    
    def add_progress_callback(self, callback: Callable):
        """Add progress callback function."""
        self.progress_callbacks.append(callback)
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific job."""
        # Check active jobs
        if job_id in self.active_jobs:
            job = self.active_jobs[job_id]
            return {
                "job_id": job.job_id,
                "status": job.status,
                "files": len(job.files),
                "data_type": job.data_type,
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "progress": "In progress"
            }
        
        # Check completed jobs
        for job in self.completed_jobs:
            if job.job_id == job_id:
                return {
                    "job_id": job.job_id,
                    "status": job.status,
                    "files": len(job.files),
                    "data_type": job.data_type,
                    "started_at": job.started_at.isoformat() if job.started_at else None,
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                    "error_message": job.error_message,
                    "results_summary": {
                        "total_rows": sum(r.stats.total_rows for r in job.results) if job.results else 0,
                        "successful_files": sum(1 for r in job.results if r.success) if job.results else 0,
                        "failed_files": sum(1 for r in job.results if not r.success) if job.results else 0
                    }
                }
        
        # Check pending jobs
        for job in self.job_queue:
            if job.job_id == job_id:
                return {
                    "job_id": job.job_id,
                    "status": job.status,
                    "files": len(job.files),
                    "data_type": job.data_type,
                    "created_at": job.created_at.isoformat(),
                    "position_in_queue": self.job_queue.index(job) + 1
                }
        
        return None
    
    def get_batch_statistics(self) -> Dict[str, Any]:
        """Get comprehensive batch processing statistics."""
        return {
            "total_jobs": self.stats.total_jobs,
            "completed_jobs": self.stats.completed_jobs,
            "failed_jobs": self.stats.failed_jobs,
            "success_rate": f"{(self.stats.completed_jobs / max(self.stats.total_jobs, 1)) * 100:.1f}%",
            "total_files": self.stats.total_files,
            "total_rows": self.stats.total_rows,
            "total_processing_time": f"{self.stats.total_processing_time:.2f}s",
            "average_throughput": f"{self.stats.average_throughput:.1f} rows/s",
            "memory_peak": f"{self.stats.memory_peak:.1f}MB",
            "active_jobs": len(self.active_jobs),
            "pending_jobs": len(self.job_queue),
            "max_workers": self.max_workers,
            "use_multiprocessing": self.use_multiprocessing
        }
    
    async def close(self):
        """Close batch processor and cleanup resources."""
        logger.info("🔄 Closing Batch Processor...")
        
        try:
            # Wait for active jobs to complete
            if self.active_jobs:
                logger.info(f"⏳ Waiting for {len(self.active_jobs)} active jobs to complete...")
                # In a real implementation, you might want to add a timeout here
            
            # Shutdown executor
            if self.executor:
                self.executor.shutdown(wait=True)
            
            logger.success("✅ Batch Processor closed successfully")
            
        except Exception as e:
            logger.error(f"❌ Batch Processor close failed: {e}")


# Convenience function for easy batch processing
async def process_csv_files_batch(
    files: List[Union[str, Path]], 
    data_type: str = "service_orders",
    max_workers: int = None
) -> Dict[str, Any]:
    """Process multiple CSV files in batch."""
    processor = BatchProcessor(max_workers=max_workers)
    await processor.initialize()
    
    job_id = await processor.submit_batch_job(files, data_type)
    result = await processor.process_batch_jobs()
    
    await processor.close()
    
    return result
