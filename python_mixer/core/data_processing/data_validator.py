#!/usr/bin/env python3
"""
Data Validator for HVAC Python Mixer
Comprehensive validation for HVAC-specific data formats
"""

import re
import asyncio
from datetime import datetime, time
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, ValidationError, Field, validator
from loguru import logger


class ServiceType(str, Enum):
    """HVAC Service types."""
    INSPECTION = "inspection"
    INSTALLATION = "installation"
    REPAIR = "repair"
    MAINTENANCE = "maintenance"
    EMERGENCY = "emergency"


class Priority(str, Enum):
    """Service priority levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TechnicianSkill(str, Enum):
    """Technician skill categories."""
    BASIC_MAINTENANCE = "basic_maintenance"
    INSTALLATION = "installation"
    ADVANCED_REPAIR = "advanced_repair"
    ELECTRICAL = "electrical"
    REFRIGERATION = "refrigeration"
    VRF_SYSTEMS = "vrf_systems"


@dataclass
class ValidationResult:
    """Validation result data structure."""
    is_valid: bool
    validated_data: Dict[str, Any]
    errors: List[str]
    warnings: List[str]


class HVACServiceOrder(BaseModel):
    """HVAC Service Order validation model."""
    id: Optional[str] = Field(None, description="Service order ID")
    customer_name: str = Field(..., min_length=2, max_length=100, description="Customer name")
    address: str = Field(..., min_length=5, max_length=200, description="Service address")
    phone: Optional[str] = Field(None, description="Customer phone number")
    email: Optional[str] = Field(None, description="Customer email")
    service_type: ServiceType = Field(..., description="Type of service")
    equipment: Optional[str] = Field(None, description="Equipment model/type")
    priority: Priority = Field(Priority.MEDIUM, description="Service priority")
    scheduled_date: Optional[str] = Field(None, description="Scheduled service date")
    technician: Optional[str] = Field(None, description="Assigned technician")
    status: Optional[str] = Field("pending", description="Order status")
    description: Optional[str] = Field(None, description="Service description")
    
    @validator('phone')
    def validate_phone(cls, v):
        if v is None:
            return v
        # Polish phone number validation
        phone_pattern = r'^(\+48\s?)?(\d{3}\s?\d{3}\s?\d{3}|\d{9})$'
        if not re.match(phone_pattern, v.replace(' ', '').replace('-', '')):
            raise ValueError('Invalid phone number format')
        return v
    
    @validator('email')
    def validate_email(cls, v):
        if v is None:
            return v
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v
    
    @validator('scheduled_date')
    def validate_date(cls, v):
        if v is None:
            return v
        # Try to parse various date formats
        date_formats = [
            '%Y-%m-%d',
            '%d/%m/%Y',
            '%d.%m.%Y',
            '%Y-%m-%d %H:%M',
            '%d/%m/%Y %H:%M'
        ]
        
        for fmt in date_formats:
            try:
                datetime.strptime(v, fmt)
                return v
            except ValueError:
                continue
        
        raise ValueError('Invalid date format')


class HVACTechnician(BaseModel):
    """HVAC Technician validation model."""
    id: Optional[str] = Field(None, description="Technician ID")
    name: str = Field(..., min_length=2, max_length=100, description="Technician name")
    skills: List[TechnicianSkill] = Field(..., description="Technician skills")
    phone: Optional[str] = Field(None, description="Contact phone")
    email: Optional[str] = Field(None, description="Contact email")
    base_location: Optional[str] = Field(None, description="Base location")
    hourly_rate: Optional[float] = Field(None, ge=0, le=1000, description="Hourly rate")
    working_hours: Optional[str] = Field(None, description="Working hours")
    max_daily_orders: Optional[int] = Field(None, ge=1, le=20, description="Max daily orders")
    
    @validator('phone')
    def validate_phone(cls, v):
        if v is None:
            return v
        phone_pattern = r'^(\+48\s?)?(\d{3}\s?\d{3}\s?\d{3}|\d{9})$'
        if not re.match(phone_pattern, v.replace(' ', '').replace('-', '')):
            raise ValueError('Invalid phone number format')
        return v
    
    @validator('email')
    def validate_email(cls, v):
        if v is None:
            return v
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v
    
    @validator('skills', pre=True)
    def validate_skills(cls, v):
        if isinstance(v, str):
            # Parse comma-separated skills
            skills_list = [skill.strip().lower().replace(' ', '_') for skill in v.split(',')]
            return skills_list
        return v


class DataValidator:
    """
    Comprehensive data validator for HVAC-specific data formats.
    
    Provides:
    - Service order validation with HVAC-specific rules
    - Technician data validation
    - Address and contact information validation
    - Equipment model validation
    - Date and time validation
    """
    
    def __init__(self):
        # Equipment model patterns
        self.equipment_patterns = {
            'lg': r'^(LG\s?)?(S\d{2}[A-Z]{2,3}|MS\d{2}[A-Z]{2,3}|MU\d{2}[A-Z]{2,3})',
            'daikin': r'^(DAIKIN\s?)?(FTX[A-Z]\d{2}[A-Z]|RXS\d{2}[A-Z])',
            'mitsubishi': r'^(MITSUBISHI\s?)?(MSZ-[A-Z]{2}\d{2}[A-Z]{2}|MUZ-[A-Z]{2}\d{2}[A-Z]{2})',
            'samsung': r'^(SAMSUNG\s?)?(AR\d{2}[A-Z]{4,6})',
            'panasonic': r'^(PANASONIC\s?)?(CS-[A-Z]{2}\d{2}[A-Z]{3}|CU-[A-Z]{2}\d{2}[A-Z]{3})'
        }
        
        # Warsaw districts for address validation
        self.warsaw_districts = [
            'bemowo', 'białołęka', 'bielany', 'mokotów', 'ochota', 'praga-południe',
            'praga-północ', 'rembertów', 'śródmieście', 'targówek', 'ursus',
            'ursynów', 'wawer', 'wesoła', 'wilanów', 'włochy', 'wola', 'żoliborz'
        ]
        
        # Validation statistics
        self.validation_stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'validation_errors': 0,
            'warnings_generated': 0
        }
    
    async def validate_service_order(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate service order data."""
        try:
            self.validation_stats['total_validations'] += 1
            
            errors = []
            warnings = []
            validated_data = {}
            
            # Preprocess data
            processed_data = self._preprocess_service_order_data(data)
            
            # Validate using Pydantic model
            try:
                service_order = HVACServiceOrder(**processed_data)
                validated_data = service_order.dict()
                
                # Additional HVAC-specific validations
                additional_warnings = await self._validate_hvac_specifics(validated_data)
                warnings.extend(additional_warnings)
                
                self.validation_stats['successful_validations'] += 1
                
                return ValidationResult(
                    is_valid=True,
                    validated_data=validated_data,
                    errors=errors,
                    warnings=warnings
                )
                
            except ValidationError as e:
                for error in e.errors():
                    field = error.get('loc', ['unknown'])[0]
                    message = error.get('msg', 'Validation error')
                    errors.append(f"{field}: {message}")
                
                self.validation_stats['validation_errors'] += 1
                
                return ValidationResult(
                    is_valid=False,
                    validated_data={},
                    errors=errors,
                    warnings=warnings
                )
                
        except Exception as e:
            logger.error(f"Service order validation failed: {e}")
            self.validation_stats['validation_errors'] += 1
            
            return ValidationResult(
                is_valid=False,
                validated_data={},
                errors=[f"Validation error: {str(e)}"],
                warnings=[]
            )
    
    async def validate_technician(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate technician data."""
        try:
            self.validation_stats['total_validations'] += 1
            
            errors = []
            warnings = []
            validated_data = {}
            
            # Preprocess data
            processed_data = self._preprocess_technician_data(data)
            
            # Validate using Pydantic model
            try:
                technician = HVACTechnician(**processed_data)
                validated_data = technician.dict()
                
                # Additional validations
                additional_warnings = await self._validate_technician_specifics(validated_data)
                warnings.extend(additional_warnings)
                
                self.validation_stats['successful_validations'] += 1
                
                return ValidationResult(
                    is_valid=True,
                    validated_data=validated_data,
                    errors=errors,
                    warnings=warnings
                )
                
            except ValidationError as e:
                for error in e.errors():
                    field = error.get('loc', ['unknown'])[0]
                    message = error.get('msg', 'Validation error')
                    errors.append(f"{field}: {message}")
                
                self.validation_stats['validation_errors'] += 1
                
                return ValidationResult(
                    is_valid=False,
                    validated_data={},
                    errors=errors,
                    warnings=warnings
                )
                
        except Exception as e:
            logger.error(f"Technician validation failed: {e}")
            self.validation_stats['validation_errors'] += 1
            
            return ValidationResult(
                is_valid=False,
                validated_data={},
                errors=[f"Validation error: {str(e)}"],
                warnings=[]
            )
    
    def _preprocess_service_order_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess service order data for validation."""
        processed = {}
        
        for key, value in data.items():
            if value is None or value == '':
                continue
                
            # Convert service_type
            if key == 'service_type' and isinstance(value, str):
                service_type_mapping = {
                    'oględziny': 'inspection',
                    'inspection': 'inspection',
                    'montaż': 'installation',
                    'instalacja': 'installation',
                    'installation': 'installation',
                    'naprawa': 'repair',
                    'serwis': 'repair',
                    'repair': 'repair',
                    'konserwacja': 'maintenance',
                    'maintenance': 'maintenance',
                    'awaria': 'emergency',
                    'emergency': 'emergency'
                }
                processed[key] = service_type_mapping.get(value.lower(), value)
            
            # Convert priority
            elif key == 'priority' and isinstance(value, str):
                priority_mapping = {
                    'niski': 'low',
                    'low': 'low',
                    'średni': 'medium',
                    'medium': 'medium',
                    'wysoki': 'high',
                    'high': 'high',
                    'pilny': 'urgent',
                    'urgent': 'urgent'
                }
                processed[key] = priority_mapping.get(value.lower(), value)
            
            else:
                processed[key] = value
        
        return processed
    
    def _preprocess_technician_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess technician data for validation."""
        processed = {}
        
        for key, value in data.items():
            if value is None or value == '':
                continue
                
            # Convert skills
            if key == 'skills' and isinstance(value, str):
                skills_mapping = {
                    'podstawowa konserwacja': 'basic_maintenance',
                    'basic maintenance': 'basic_maintenance',
                    'montaż': 'installation',
                    'installation': 'installation',
                    'zaawansowane naprawy': 'advanced_repair',
                    'advanced repair': 'advanced_repair',
                    'elektryka': 'electrical',
                    'electrical': 'electrical',
                    'chłodnictwo': 'refrigeration',
                    'refrigeration': 'refrigeration',
                    'vrf': 'vrf_systems',
                    'vrf systems': 'vrf_systems'
                }
                
                skills_list = []
                for skill in value.split(','):
                    skill = skill.strip().lower()
                    mapped_skill = skills_mapping.get(skill, skill.replace(' ', '_'))
                    skills_list.append(mapped_skill)
                
                processed[key] = skills_list
            
            else:
                processed[key] = value
        
        return processed
    
    async def _validate_hvac_specifics(self, data: Dict[str, Any]) -> List[str]:
        """Additional HVAC-specific validations."""
        warnings = []
        
        # Validate equipment model
        if 'equipment' in data and data['equipment']:
            equipment = data['equipment'].upper()
            is_valid_model = False
            
            for brand, pattern in self.equipment_patterns.items():
                if re.match(pattern, equipment, re.IGNORECASE):
                    is_valid_model = True
                    break
            
            if not is_valid_model:
                warnings.append(f"Equipment model '{equipment}' doesn't match known HVAC patterns")
        
        # Validate address for Warsaw area
        if 'address' in data and data['address']:
            address = data['address'].lower()
            is_warsaw = any(district in address for district in self.warsaw_districts)
            if not is_warsaw and 'warszawa' not in address:
                warnings.append("Address doesn't appear to be in Warsaw area")
        
        # Validate service type and priority combination
        if data.get('service_type') == 'emergency' and data.get('priority') not in ['high', 'urgent']:
            warnings.append("Emergency service should have high or urgent priority")
        
        return warnings
    
    async def _validate_technician_specifics(self, data: Dict[str, Any]) -> List[str]:
        """Additional technician-specific validations."""
        warnings = []
        
        # Validate skill combinations
        skills = data.get('skills', [])
        if 'vrf_systems' in skills and 'advanced_repair' not in skills:
            warnings.append("VRF systems skill usually requires advanced repair skills")
        
        # Validate hourly rate
        hourly_rate = data.get('hourly_rate')
        if hourly_rate:
            if hourly_rate < 50:
                warnings.append("Hourly rate seems low for HVAC technician")
            elif hourly_rate > 200:
                warnings.append("Hourly rate seems high, please verify")
        
        # Validate daily capacity
        max_orders = data.get('max_daily_orders')
        if max_orders and max_orders > 10:
            warnings.append("More than 10 daily orders might be unrealistic")
        
        return warnings
    
    def get_validation_statistics(self) -> Dict[str, Any]:
        """Get validation performance statistics."""
        total = self.validation_stats['total_validations']
        success_rate = (self.validation_stats['successful_validations'] / max(total, 1)) * 100
        error_rate = (self.validation_stats['validation_errors'] / max(total, 1)) * 100
        
        return {
            'total_validations': total,
            'successful_validations': self.validation_stats['successful_validations'],
            'validation_errors': self.validation_stats['validation_errors'],
            'warnings_generated': self.validation_stats['warnings_generated'],
            'success_rate': f"{success_rate:.1f}%",
            'error_rate': f"{error_rate:.1f}%"
        }
