#!/usr/bin/env python3
"""
Enhanced CSV Data Processing for HVAC Python Mixer
Robust error handling, validation, and batch processing for HVAC-specific data
"""

import asyncio
import csv
import io
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple, Iterator
from dataclasses import dataclass, asdict
import json

import pandas as pd
import numpy as np
from pydantic import BaseModel, ValidationError, Field
from loguru import logger

from .data_validator import DataValidator, ValidationResult


@dataclass
class ProcessingStats:
    """CSV processing statistics."""
    total_rows: int
    processed_rows: int
    error_rows: int
    validation_errors: int
    processing_time: float
    memory_usage: float
    throughput: float  # rows per second


@dataclass
class ProcessingResult:
    """CSV processing result."""
    success: bool
    stats: ProcessingStats
    valid_data: List[Dict[str, Any]]
    errors: List[Dict[str, Any]]
    warnings: List[str]
    summary: str


class EnhancedCSVProcessor:
    """
    Enhanced CSV Processor for HVAC data with robust error handling.
    
    Features:
    - Robust error handling for malformed CSV files
    - Data validation and type conversion
    - Support for HVAC-specific data formats
    - Batch processing capabilities for large datasets
    - Integration with existing datatoingest pipeline
    - Performance monitoring and optimization
    """
    
    def __init__(self, chunk_size: int = 1000, max_memory_mb: int = 500):
        self.chunk_size = chunk_size
        self.max_memory_mb = max_memory_mb
        self.validator = DataValidator()
        
        # Processing statistics
        self.processing_history = []
        self.performance_metrics = {
            "total_files_processed": 0,
            "total_rows_processed": 0,
            "average_processing_time": 0.0,
            "error_rate": 0.0
        }
        
        # HVAC-specific column mappings
        self.hvac_column_mappings = {
            # Service Orders
            "service_orders": {
                "id": ["id", "order_id", "service_id", "ticket_id"],
                "customer_name": ["customer", "customer_name", "client", "client_name"],
                "address": ["address", "location", "site_address", "service_address"],
                "phone": ["phone", "telephone", "contact_number", "mobile"],
                "email": ["email", "email_address", "contact_email"],
                "service_type": ["service_type", "type", "service", "work_type"],
                "equipment": ["equipment", "unit", "model", "equipment_model"],
                "priority": ["priority", "urgency", "priority_level"],
                "scheduled_date": ["date", "scheduled_date", "appointment_date", "service_date"],
                "technician": ["technician", "tech", "assigned_tech", "engineer"],
                "status": ["status", "state", "order_status"],
                "description": ["description", "notes", "details", "work_description"]
            },
            # Technicians
            "technicians": {
                "id": ["id", "tech_id", "employee_id"],
                "name": ["name", "technician_name", "full_name"],
                "skills": ["skills", "specialization", "expertise", "certifications"],
                "phone": ["phone", "mobile", "contact"],
                "email": ["email", "email_address"],
                "base_location": ["location", "base", "home_base", "depot"],
                "hourly_rate": ["rate", "hourly_rate", "cost_per_hour"],
                "working_hours": ["hours", "working_hours", "shift"],
                "max_daily_orders": ["capacity", "max_orders", "daily_limit"]
            }
        }
    
    async def process_csv_file(
        self, 
        file_path: Union[str, Path], 
        data_type: str = "service_orders",
        encoding: str = "utf-8"
    ) -> ProcessingResult:
        """Process CSV file with enhanced error handling and validation."""
        start_time = time.time()
        file_path = Path(file_path)
        
        logger.info(f"📊 Processing CSV file: {file_path.name}")
        logger.info(f"🎯 Data type: {data_type}")
        
        try:
            # Validate file exists and is readable
            if not file_path.exists():
                raise FileNotFoundError(f"CSV file not found: {file_path}")
            
            if file_path.stat().st_size == 0:
                raise ValueError("CSV file is empty")
            
            # Detect encoding if needed
            detected_encoding = await self._detect_encoding(file_path)
            if detected_encoding != encoding:
                logger.info(f"🔍 Detected encoding: {detected_encoding}, using: {encoding}")
            
            # Process file in chunks for memory efficiency
            processing_result = await self._process_file_chunks(
                file_path, data_type, encoding
            )
            
            processing_time = time.time() - start_time
            processing_result.stats.processing_time = processing_time
            
            # Update performance metrics
            self._update_performance_metrics(processing_result)
            
            # Log results
            self._log_processing_results(processing_result, file_path.name)
            
            return processing_result
            
        except Exception as e:
            logger.error(f"❌ CSV processing failed: {e}")
            
            return ProcessingResult(
                success=False,
                stats=ProcessingStats(0, 0, 0, 0, time.time() - start_time, 0.0, 0.0),
                valid_data=[],
                errors=[{"error": str(e), "type": "file_error"}],
                warnings=[],
                summary=f"Processing failed: {str(e)}"
            )
    
    async def process_csv_data(
        self, 
        csv_data: str, 
        data_type: str = "service_orders"
    ) -> ProcessingResult:
        """Process CSV data from string with validation."""
        start_time = time.time()
        
        logger.info(f"📊 Processing CSV data string ({len(csv_data)} characters)")
        
        try:
            # Create StringIO object for pandas
            csv_buffer = io.StringIO(csv_data)
            
            # Process data
            processing_result = await self._process_data_chunks(
                csv_buffer, data_type
            )
            
            processing_result.stats.processing_time = time.time() - start_time
            
            return processing_result
            
        except Exception as e:
            logger.error(f"❌ CSV data processing failed: {e}")
            
            return ProcessingResult(
                success=False,
                stats=ProcessingStats(0, 0, 0, 0, time.time() - start_time, 0.0, 0.0),
                valid_data=[],
                errors=[{"error": str(e), "type": "data_error"}],
                warnings=[],
                summary=f"Data processing failed: {str(e)}"
            )
    
    async def _process_file_chunks(
        self, 
        file_path: Path, 
        data_type: str, 
        encoding: str
    ) -> ProcessingResult:
        """Process CSV file in chunks for memory efficiency."""
        valid_data = []
        errors = []
        warnings = []
        total_rows = 0
        processed_rows = 0
        error_rows = 0
        validation_errors = 0
        
        try:
            # Read CSV in chunks
            chunk_iterator = pd.read_csv(
                file_path,
                encoding=encoding,
                chunksize=self.chunk_size,
                dtype=str,  # Read all as strings initially
                na_filter=False,  # Don't convert to NaN
                on_bad_lines='warn'  # Warn about bad lines but continue
            )
            
            for chunk_num, chunk in enumerate(chunk_iterator):
                logger.debug(f"Processing chunk {chunk_num + 1} ({len(chunk)} rows)")
                
                # Process chunk
                chunk_result = await self._process_chunk(chunk, data_type)
                
                # Aggregate results
                valid_data.extend(chunk_result["valid_data"])
                errors.extend(chunk_result["errors"])
                warnings.extend(chunk_result["warnings"])
                
                total_rows += len(chunk)
                processed_rows += chunk_result["processed"]
                error_rows += chunk_result["errors_count"]
                validation_errors += chunk_result["validation_errors"]
                
                # Memory check
                if self._check_memory_usage():
                    warnings.append(f"High memory usage detected at chunk {chunk_num + 1}")
            
            # Calculate throughput
            processing_time = time.time()
            throughput = total_rows / max(processing_time, 0.001)
            
            stats = ProcessingStats(
                total_rows=total_rows,
                processed_rows=processed_rows,
                error_rows=error_rows,
                validation_errors=validation_errors,
                processing_time=0.0,  # Will be set by caller
                memory_usage=self._get_memory_usage(),
                throughput=throughput
            )
            
            success = error_rows < (total_rows * 0.5)  # Success if < 50% errors
            
            summary = self._generate_processing_summary(stats, success)
            
            return ProcessingResult(
                success=success,
                stats=stats,
                valid_data=valid_data,
                errors=errors,
                warnings=warnings,
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Chunk processing failed: {e}")
            raise
    
    async def _process_data_chunks(self, csv_buffer: io.StringIO, data_type: str) -> ProcessingResult:
        """Process CSV data from buffer."""
        try:
            # Read all data at once for string input
            df = pd.read_csv(
                csv_buffer,
                dtype=str,
                na_filter=False,
                on_bad_lines='warn'
            )
            
            # Process as single chunk
            chunk_result = await self._process_chunk(df, data_type)
            
            stats = ProcessingStats(
                total_rows=len(df),
                processed_rows=chunk_result["processed"],
                error_rows=chunk_result["errors_count"],
                validation_errors=chunk_result["validation_errors"],
                processing_time=0.0,
                memory_usage=self._get_memory_usage(),
                throughput=len(df) / max(time.time(), 0.001)
            )
            
            success = chunk_result["errors_count"] < (len(df) * 0.5)
            summary = self._generate_processing_summary(stats, success)
            
            return ProcessingResult(
                success=success,
                stats=stats,
                valid_data=chunk_result["valid_data"],
                errors=chunk_result["errors"],
                warnings=chunk_result["warnings"],
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Data chunk processing failed: {e}")
            raise
    
    async def _process_chunk(self, chunk: pd.DataFrame, data_type: str) -> Dict[str, Any]:
        """Process a single chunk of data."""
        valid_data = []
        errors = []
        warnings = []
        processed = 0
        errors_count = 0
        validation_errors = 0
        
        try:
            # Normalize column names
            normalized_chunk = self._normalize_columns(chunk, data_type)
            
            # Process each row
            for index, row in normalized_chunk.iterrows():
                try:
                    # Convert row to dict and clean
                    row_data = self._clean_row_data(row.to_dict())
                    
                    # Validate data based on type
                    validation_result = await self._validate_row_data(row_data, data_type)
                    
                    if validation_result.is_valid:
                        valid_data.append(validation_result.validated_data)
                        processed += 1
                    else:
                        errors.append({
                            "row": index + 1,
                            "data": row_data,
                            "errors": validation_result.errors,
                            "type": "validation_error"
                        })
                        validation_errors += 1
                        errors_count += 1
                    
                    # Add warnings if any
                    if validation_result.warnings:
                        warnings.extend([
                            f"Row {index + 1}: {warning}" 
                            for warning in validation_result.warnings
                        ])
                
                except Exception as e:
                    errors.append({
                        "row": index + 1,
                        "data": row.to_dict() if hasattr(row, 'to_dict') else str(row),
                        "error": str(e),
                        "type": "processing_error"
                    })
                    errors_count += 1
            
            return {
                "valid_data": valid_data,
                "errors": errors,
                "warnings": warnings,
                "processed": processed,
                "errors_count": errors_count,
                "validation_errors": validation_errors
            }
            
        except Exception as e:
            logger.error(f"Chunk processing error: {e}")
            raise
    
    def _normalize_columns(self, df: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """Normalize column names based on HVAC data type."""
        try:
            if data_type not in self.hvac_column_mappings:
                logger.warning(f"Unknown data type: {data_type}, using as-is")
                return df
            
            column_mapping = self.hvac_column_mappings[data_type]
            normalized_df = df.copy()
            
            # Create reverse mapping (original -> standard)
            reverse_mapping = {}
            for standard_col, possible_names in column_mapping.items():
                for possible_name in possible_names:
                    # Case-insensitive matching
                    for col in df.columns:
                        if col.lower().strip() == possible_name.lower():
                            reverse_mapping[col] = standard_col
                            break
            
            # Rename columns
            if reverse_mapping:
                normalized_df = normalized_df.rename(columns=reverse_mapping)
                logger.debug(f"Normalized {len(reverse_mapping)} columns")
            
            return normalized_df
            
        except Exception as e:
            logger.error(f"Column normalization failed: {e}")
            return df
    
    def _clean_row_data(self, row_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and standardize row data."""
        cleaned_data = {}
        
        for key, value in row_data.items():
            # Clean key
            clean_key = key.strip().lower().replace(' ', '_')
            
            # Clean value
            if isinstance(value, str):
                clean_value = value.strip()
                # Convert empty strings to None
                if clean_value == '' or clean_value.lower() in ['null', 'none', 'n/a', 'na']:
                    clean_value = None
            else:
                clean_value = value
            
            cleaned_data[clean_key] = clean_value
        
        return cleaned_data
    
    async def _validate_row_data(self, row_data: Dict[str, Any], data_type: str) -> ValidationResult:
        """Validate row data based on HVAC data type."""
        try:
            if data_type == "service_orders":
                return await self.validator.validate_service_order(row_data)
            elif data_type == "technicians":
                return await self.validator.validate_technician(row_data)
            else:
                # Generic validation
                return ValidationResult(
                    is_valid=True,
                    validated_data=row_data,
                    errors=[],
                    warnings=[]
                )
                
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                validated_data={},
                errors=[f"Validation error: {str(e)}"],
                warnings=[]
            )
    
    async def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        try:
            import chardet
            
            with open(file_path, 'rb') as f:
                raw_data = f.read(10000)  # Read first 10KB
                result = chardet.detect(raw_data)
                return result.get('encoding', 'utf-8')
                
        except ImportError:
            logger.warning("chardet not available, using utf-8")
            return 'utf-8'
        except Exception as e:
            logger.warning(f"Encoding detection failed: {e}, using utf-8")
            return 'utf-8'
    
    def _check_memory_usage(self) -> bool:
        """Check if memory usage is too high."""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            return memory_mb > self.max_memory_mb
        except ImportError:
            return False
        except Exception:
            return False
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
        except Exception:
            return 0.0
    
    def _generate_processing_summary(self, stats: ProcessingStats, success: bool) -> str:
        """Generate processing summary."""
        success_rate = ((stats.processed_rows / stats.total_rows) * 100) if stats.total_rows > 0 else 0
        
        summary = f"""
📊 CSV Processing Summary:
✅ Success: {success}
📈 Processed: {stats.processed_rows}/{stats.total_rows} rows ({success_rate:.1f}%)
❌ Errors: {stats.error_rows} rows
⚠️ Validation Issues: {stats.validation_errors}
⚡ Throughput: {stats.throughput:.1f} rows/second
💾 Memory Usage: {stats.memory_usage:.1f} MB
        """.strip()
        
        return summary
    
    def _update_performance_metrics(self, result: ProcessingResult):
        """Update performance metrics."""
        self.performance_metrics["total_files_processed"] += 1
        self.performance_metrics["total_rows_processed"] += result.stats.total_rows
        
        # Update average processing time
        current_avg = self.performance_metrics["average_processing_time"]
        new_avg = ((current_avg * (self.performance_metrics["total_files_processed"] - 1)) + 
                  result.stats.processing_time) / self.performance_metrics["total_files_processed"]
        self.performance_metrics["average_processing_time"] = new_avg
        
        # Update error rate
        total_errors = sum(len(r.errors) for r in self.processing_history) + len(result.errors)
        total_processed = sum(r.stats.total_rows for r in self.processing_history) + result.stats.total_rows
        self.performance_metrics["error_rate"] = (total_errors / max(total_processed, 1)) * 100
        
        # Store result
        self.processing_history.append(result)
        
        # Keep only last 100 results
        if len(self.processing_history) > 100:
            self.processing_history = self.processing_history[-100:]
    
    def _log_processing_results(self, result: ProcessingResult, filename: str):
        """Log processing results."""
        if result.success:
            logger.success(f"✅ {filename} processed successfully")
            logger.info(f"📊 {result.stats.processed_rows}/{result.stats.total_rows} rows processed")
            logger.info(f"⚡ {result.stats.throughput:.1f} rows/second")
        else:
            logger.error(f"❌ {filename} processing failed")
            logger.error(f"💥 {result.stats.error_rows} errors out of {result.stats.total_rows} rows")
        
        if result.warnings:
            logger.warning(f"⚠️ {len(result.warnings)} warnings generated")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        return {
            "total_files_processed": self.performance_metrics["total_files_processed"],
            "total_rows_processed": self.performance_metrics["total_rows_processed"],
            "average_processing_time": f"{self.performance_metrics['average_processing_time']:.2f}s",
            "error_rate": f"{self.performance_metrics['error_rate']:.2f}%",
            "recent_results": len(self.processing_history),
            "chunk_size": self.chunk_size,
            "max_memory_mb": self.max_memory_mb
        }
