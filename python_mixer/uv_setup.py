#!/usr/bin/env python3
"""
UV Virtual Environment Setup for HVAC Python Mixer
Ultra-fast Python package installer and resolver setup
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from typing import Optional, List
import json

from loguru import logger


class UVEnvironmentManager:
    """
    UV Environment Manager for HVAC Python Mixer
    
    Provides ultra-fast dependency resolution and installation
    with better reproducible builds and improved performance.
    """
    
    def __init__(self, project_root: Optional[Path] = None):
        self.project_root = project_root or Path(__file__).parent
        self.pyproject_path = self.project_root / "pyproject.toml"
        self.uv_lock_path = self.project_root / "uv.lock"
        self.venv_path = self.project_root / ".venv"
        
        # Performance tracking
        self.installation_times = []
        self.dependency_count = 0
        
    def check_uv_installation(self) -> bool:
        """Check if UV is installed and available."""
        try:
            result = subprocess.run(
                ["uv", "--version"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                logger.info(f"UV found: {result.stdout.strip()}")
                return True
            else:
                logger.error("UV not found or not working properly")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            logger.error(f"UV check failed: {e}")
            return False
    
    def install_uv(self) -> bool:
        """Install UV if not available."""
        logger.info("Installing UV...")
        try:
            # Install UV using the official installer
            install_cmd = [
                sys.executable, "-m", "pip", "install", "uv"
            ]
            result = subprocess.run(install_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.success("UV installed successfully")
                return True
            else:
                logger.error(f"UV installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to install UV: {e}")
            return False
    
    def create_virtual_environment(self) -> bool:
        """Create UV virtual environment."""
        logger.info("Creating UV virtual environment...")
        try:
            # Remove existing venv if it exists
            if self.venv_path.exists():
                logger.info("Removing existing virtual environment...")
                import shutil
                shutil.rmtree(self.venv_path)
            
            # Create new UV virtual environment
            create_cmd = ["uv", "venv", str(self.venv_path)]
            result = subprocess.run(create_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.success(f"Virtual environment created at {self.venv_path}")
                return True
            else:
                logger.error(f"Failed to create virtual environment: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Virtual environment creation failed: {e}")
            return False
    
    def install_dependencies(self, dev: bool = False) -> bool:
        """Install dependencies using UV."""
        start_time = time.time()
        
        logger.info("Installing dependencies with UV...")
        try:
            # Base installation command
            install_cmd = ["uv", "pip", "install", "-e", "."]
            
            # Add dev dependencies if requested
            if dev:
                install_cmd.extend(["--extra", "dev"])
                logger.info("Including development dependencies")
            
            # Run installation
            result = subprocess.run(
                install_cmd, 
                capture_output=True, 
                text=True,
                cwd=self.project_root
            )
            
            installation_time = time.time() - start_time
            self.installation_times.append(installation_time)
            
            if result.returncode == 0:
                logger.success(f"Dependencies installed in {installation_time:.2f}s")
                self._log_performance_metrics()
                return True
            else:
                logger.error(f"Dependency installation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Dependency installation error: {e}")
            return False
    
    def sync_dependencies(self) -> bool:
        """Sync dependencies with lock file."""
        logger.info("Syncing dependencies with UV...")
        try:
            sync_cmd = ["uv", "pip", "sync", "requirements.txt"]
            result = subprocess.run(sync_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.success("Dependencies synced successfully")
                return True
            else:
                logger.warning(f"Sync warning: {result.stderr}")
                return True  # Warnings are acceptable
                
        except Exception as e:
            logger.error(f"Dependency sync failed: {e}")
            return False
    
    def generate_lock_file(self) -> bool:
        """Generate UV lock file for reproducible builds."""
        logger.info("Generating UV lock file...")
        try:
            # Export current environment to requirements format
            export_cmd = ["uv", "pip", "freeze"]
            result = subprocess.run(export_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Write lock file
                with open(self.uv_lock_path, 'w') as f:
                    f.write(result.stdout)
                logger.success(f"Lock file generated: {self.uv_lock_path}")
                return True
            else:
                logger.error(f"Lock file generation failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Lock file generation error: {e}")
            return False
    
    def _log_performance_metrics(self):
        """Log performance metrics for UV installation."""
        if self.installation_times:
            avg_time = sum(self.installation_times) / len(self.installation_times)
            logger.info(f"Average installation time: {avg_time:.2f}s")
            
            # Compare with typical pip performance (estimated)
            estimated_pip_time = avg_time * 2.5  # UV is typically 2-3x faster
            improvement = ((estimated_pip_time - avg_time) / estimated_pip_time) * 100
            logger.info(f"Estimated performance improvement over pip: {improvement:.1f}%")
    
    def setup_complete_environment(self, include_dev: bool = True) -> bool:
        """Complete UV environment setup."""
        logger.info("🚀 Starting UV Environment Setup for HVAC Python Mixer")
        
        # Step 1: Check/Install UV
        if not self.check_uv_installation():
            if not self.install_uv():
                logger.error("Failed to install UV")
                return False
        
        # Step 2: Create virtual environment
        if not self.create_virtual_environment():
            logger.error("Failed to create virtual environment")
            return False
        
        # Step 3: Install dependencies
        if not self.install_dependencies(dev=include_dev):
            logger.error("Failed to install dependencies")
            return False
        
        # Step 4: Generate lock file
        if not self.generate_lock_file():
            logger.warning("Failed to generate lock file (non-critical)")
        
        logger.success("🎉 UV Environment Setup Complete!")
        logger.info(f"Virtual environment: {self.venv_path}")
        logger.info(f"Lock file: {self.uv_lock_path}")
        
        return True
    
    def get_activation_command(self) -> str:
        """Get the command to activate the UV virtual environment."""
        if os.name == 'nt':  # Windows
            return f"{self.venv_path}\\Scripts\\activate"
        else:  # Unix/Linux/macOS
            return f"source {self.venv_path}/bin/activate"


def main():
    """Main UV setup function."""
    logger.info("HVAC Python Mixer - UV Environment Setup")
    
    uv_manager = UVEnvironmentManager()
    
    # Setup complete environment with dev dependencies
    success = uv_manager.setup_complete_environment(include_dev=True)
    
    if success:
        logger.success("✅ UV setup completed successfully!")
        logger.info(f"To activate: {uv_manager.get_activation_command()}")
        logger.info("To run interface: python enhanced_human_comprehension_interface.py")
    else:
        logger.error("❌ UV setup failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
