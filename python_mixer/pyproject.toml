[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hvac-python-mixer"
version = "2.0.0"
description = "Enhanced HVAC Multi-Agent System with Human Comprehension Interface"
authors = [
    {name = "HVAC CRM Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
keywords = ["hvac", "crm", "ai", "agents", "gradio", "mcp"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Office/Business :: Financial :: Point-Of-Sale",
]

dependencies = [
    # Multi-Agent Frameworks
    "langgraph>=0.2.0",
    "crewai>=0.70.0",
    "langroid>=0.21.0",
    "agno>=0.1.0",
    "openai-swarm>=1.0.0",
    
    # MCP Integration
    "mcp>=1.0.0",
    "mcp-client>=1.0.0",
    
    # Graphiti Knowledge Graph
    "graphiti-core>=0.11.6",
    "pydantic-ai>=0.2.9",
    "pydantic-ai-slim>=0.2.9",
    
    # LangChain ecosystem
    "langchain>=0.3.0",
    "langchain-community>=0.3.0",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.2.0",
    
    # OpenAI and AI models
    "openai>=1.50.0",
    "anthropic>=0.34.0",
    
    # Email processing
    "imaplib2>=3.6",
    "email-validator>=2.1.0",
    "flanker>=0.9.15",
    "yagmail>=0.15.293",
    
    # Database integrations
    "sqlalchemy>=2.0.0",
    "pymongo>=4.6.0",
    "neo4j>=5.15.0",
    "psycopg2-binary>=2.9.9",
    "redis>=5.0.0",
    
    # GraphQL
    "graphene>=3.3.0",
    "graphql-core>=3.2.3",
    "ariadne>=0.22.0",
    
    # Web frameworks
    "flask>=3.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    
    # Data processing
    "pandas>=2.1.0",
    "numpy>=1.24.0",
    "pydantic>=2.5.0",
    
    # UI and visualization
    "gradio>=4.0.0",
    "plotly>=5.17.0",
    "loguru>=0.7.0",
    "rich>=14.0.0",
    "streamlit>=1.45.0",
    
    # Document Processing
    "PyPDF2>=3.0.1",
    "pdfplumber>=0.10.0",
    "python-docx>=1.1.0",
    "openpyxl>=3.1.2",
    "Pillow>=10.0.0",
    "pytesseract>=0.3.10",
    "opencv-python>=4.8.0",
    "pdf2image>=1.16.3",
    "PyMuPDF>=1.23.0",
    "langdetect>=1.0.9",
    
    # Audio Processing
    "librosa>=0.10.1",
    "soundfile>=0.12.1",
    "pydub>=0.25.1",
    "scipy>=1.11.0",
    
    # Task Queue
    "celery>=5.3.0",
    
    # Additional ML/AI
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    
    # Async support
    "asyncio-mqtt>=0.16.0",
    "aiofiles>=23.2.1",
    "aiohttp>=3.9.0",
    
    # Logging and monitoring
    "structlog>=23.2.0",
    "python-json-logger>=2.0.7",
    
    # Configuration
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.1",
    
    # Calendar and Route Optimization
    "ortools>=9.8.0",
    "geopy>=2.4.0",
    "folium>=0.15.0",
    
    # HTTP clients
    "httpx>=0.25.0",
    "requests>=2.31.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.11.0",
    "flake8>=6.1.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
]

test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
]

docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]

[project.urls]
Homepage = "https://github.com/hvac-crm/python-mixer"
Documentation = "https://hvac-crm.github.io/python-mixer"
Repository = "https://github.com/hvac-crm/python-mixer"
Issues = "https://github.com/hvac-crm/python-mixer/issues"

[project.scripts]
hvac-mixer = "python_mixer.main:main"
hvac-interface = "python_mixer.enhanced_human_comprehension_interface:main"
hvac-calendar = "python_mixer.demo_calendar_agent:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["python_mixer*"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["python_mixer"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
